tar -zcvf ebook-api.tar.gz --exclude=node_modules --exclude=docker-entrypoint-initdb --exclude=docker-compose.yml --exclude=deploy.sh ./*
sudo scp -i ./jrc-testing.pem ebook-api.tar.gz ubuntu@122.248.233.120://home/<USER>/staging/ebook-api/
if [ -f "ebook-api.tar.gz" ]; then
   rm ebook-api.tar.gz
fi
sudo ssh -i ./jrc-testing.pem ubuntu@122.248.233.120 "cd /home/<USER>/staging/ebook-api && sh deploy.sh"

