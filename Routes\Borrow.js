const app = require('express').Router();
const fs = require('fs');
const {
  UserHelper
} = require('../Helper/UserHelper');
const Borrow = require('../Model/Borrow');
const { batchReturnBorrowedBooks } = require('../src/action/borrowAction');
const { syncReturnBooks } = require('../src/action/bookActions');
const Book = require('../Model/Book');
const Reserve = require('../Model/Reserve');
const multer = require('multer');
const UserSession = require('../Model/UserSession');
const upload = multer();
const BookHelper = require('../Helper/BookHelper');
const User = require('../Model/User');
const mongoose = require('mongoose');
const SystemLog = require('../Model/SystemLog');
const { MAX_BORROW_BOOK_NUM, MAX_BORROW_DAYS } = require('../consts');

// 内存缓存
// ===== 缓存管理工具 =====
const cache = {};

/**
 * 缓存管理工具类
 * 提供更优雅的缓存操作和自动清理机制
 */
class CacheManager {
  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {string} operation - 操作类型
   * @param {number} expireMinutes - 过期时间（分钟）
   */
  static set(key, operation, expireMinutes = 10) {
    const expireTime = Date.now() + expireMinutes * 60 * 1000;
    cache[key] = {
      timestamp: Date.now(),
      expireTime: expireTime,
      operation: operation
    };

    // 设置自动清理定时器（兜底保障）
    setTimeout(() => {
      if (cache[key] && Date.now() >= cache[key].expireTime) {
        delete cache[key];
        console.log(`Cache auto-expired: ${key}`);
      }
    }, expireMinutes * 60 * 1000);

    console.log(`Cache set: ${key} (expires in ${expireMinutes} minutes)`);
  }

  /**
   * 检查缓存是否存在
   * @param {string} key - 缓存键
   * @returns {boolean} 是否存在
   */
  static exists(key) {
    if (!cache[key]) return false;

    // 检查是否过期
    if (Date.now() >= cache[key].expireTime) {
      delete cache[key];
      console.log(`Cache expired and removed: ${key}`);
      return false;
    }

    return true;
  }

  /**
   * 清除缓存
   * @param {string} key - 缓存键
   * @param {string} reason - 清除原因
   */
  static clear(key, reason = 'normal') {
    if (cache[key]) {
      delete cache[key];
      console.log(`Cache cleared (${reason}): ${key}`);
    }
  }

  /**
   * 清理所有过期缓存
   */
  static cleanExpired() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const key in cache) {
      if (cache[key].expireTime <= now) {
        delete cache[key];
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`Cleaned ${cleanedCount} expired cache entries`);
    }
  }
}

// 定期清理过期缓存（每5分钟执行一次）
setInterval(() => {
  CacheManager.cleanExpired();
}, 5 * 60 * 1000);
// 设置缓存过期时间（单位：毫秒）
const CACHE_EXPIRY_TIME = 60000; // 1分钟



app.get('/auto-logout-user', async (req, res) => {
  console.log('every 5 min');
  const loggedin_users = [];
  await UserSession.find({ is_active: true }).then(async (resp) => {
    resp.forEach(async (resp_el) => {
      const respdatetodate = new Date(resp_el.timestamp);
      const todaydate = new Date();
      respdatetodate.setMinutes(respdatetodate.getMinutes() + 60);
      if (todaydate > respdatetodate) {
        await UserSession.updateOne({
          _id: mongoose.Types.ObjectId(resp_el._id)
        }, {
          $set: {
            is_active: false
          }
        }, function (err, res) {
          if (err) throw err;
          loggedin_users.push(resp_el);
        });
      }
    });
  });

  return res.send({
    code: 200,
    data: loggedin_users,
    message: 'User will auto-logout after 60 min of login time.'
  });
});

app.get('/sync-return-books', async (req, res) => {
  const message = 'book return cron will run every day at 12:00 AM on ' + new Date();
  const output = '';
  await syncReturnBooks();

  console.log({ code: 200, data: output, message: message });
  return res.send({
    code: 200,
    data: output,
    message: message
  });
});

app.use('/*', (req, res, next) => next());

app.post('/book', upload.none(), UserHelper, async (req, res) => {
  if (
    !req.body.user_id ||
        !req.body.book_id ||
        !req.body.issue_date ||
        !req.body.return_date ||
        !req.body.status
  ) {
    return res.json({
      code: 418,
      message: 'Missing required fields',
      status: false
    });
  }

  // ===== 防重复提交机制 =====
  // 同一用户同一本书只能借阅一次，防止并发重复请求
  const cacheKey = 'borrow_' + req.body.user_id + '_' + req.body.book_id;

  // 检查是否正在处理中
  if (CacheManager.exists(cacheKey)) {
    return res.json({
      code: 418,
      message: 'Processing in progress',
      status: false
    });
  }

  // 设置缓存标记，10分钟过期
  // 主要依靠正常流程删除，自动过期只是兜底保障
  CacheManager.set(cacheKey, 'borrow', 10);

  const _user = await User.findOne({
    _id: new mongoose.Types.ObjectId(req.body.user_id)
  });
  if (!_user) {
    const systemLog = SystemLog({
      ctime: new Date(),
      data: {
        body: req.body,
        token: req.header('SESSION-TOKEN')
      }
    });
    await systemLog.save();
  }

  const book = await Book.findOne({ _id: new mongoose.Types.ObjectId(req.body.book_id) });

  const borrowLogs = await Borrow.find({
    book_id: mongoose.Types.ObjectId(book._id),
    user_id: mongoose.Types.ObjectId(_user._id),
    returned: false,
    is_deleted: false
  });
  if (borrowLogs && borrowLogs.length > 0) {
    res.json({
      code: 420,
      message: 'You\'ve borrowed this book',
      status: false
    });
    // 正常流程清除缓存
    CacheManager.clear(cacheKey, 'already borrowed');
    return res.end();
  }

  const issue_date = req.body.issue_date;//moment.unix(req.body.issue_date).tz('Asia/Hong_Kong').toDate();//.startOf('day').toDate();
  const return_date = req.body.return_date;//moment.unix(req.body.return_date).tz('Asia/Hong_Kong').toDate();//.endOf('day').toDate();

  const valid = true;
  // var issue_date = new Date();
  //issue_date.setHours(0, 0, 0, 0);
  // const maxReturnDays = MAX_BORROW_DAYS; // 最大可借阅天数
  const maxBorrowBooks = MAX_BORROW_BOOK_NUM; // 最大可借阅本书
  /*var date = new Date();
    //date.setHours(0, 0, 0, 0);
    date.setDate(date.getDate() + maxReturnDays);
    date.setHours(23, 59, 0, 0);
    const return_date = date;*/
  if (valid == true) {
    await User.find({
      _id: {
        $ne: req.body.user_id
      }
    }).then(async (resx) => {
      Borrow.find({
        user_id: mongoose.Types.ObjectId(req.body.user_id)
      }).then(async (resx) => {

        if (await BookHelper.totalBookBorrowedInPresent(req.body.user_id, book.collection_type) >= maxBorrowBooks) {
          res.json({
            code: 420,
            message: 'You\'ve reached maximum limit of borrow!',
            status: false
          });
          // 正常流程清除缓存
          CacheManager.clear(cacheKey, 'borrow limit reached');
          return res.end();
        } else if (await BookHelper.isBookBorrowed(req.body.user_id, req.body.book_id)) {
          res.json({
            code: 421,
            message: 'You\'ve already borrowed this!',
            status: false
          });
          // 正常流程清除缓存
          CacheManager.clear(cacheKey, 'already borrowed this book');
          return res.end();
        } else {
          // 使用原子操作檢查庫存並創建借閱記錄
          const user_id = req.body.user_id;
          const book_id = req.body.book_id;

          // ===== 事務前預檢查：使用 isBookInStock() 做初步庫存檢查 =====
          const preCheckedQuantity = await BookHelper.isBookInStock(book_id);
          if (preCheckedQuantity <= 0) {
            res.json({
              code: 422,
              message: 'Book is out of stock!',
              status: false
            });
            CacheManager.clear(cacheKey, 'book out of stock');
            return res.end();
          }

          // 使用 MongoDB 事務確保原子性
          const session = await mongoose.startSession();

          try {
            await session.withTransaction(async () => {
              // ===== 事務內部：只使用原子操作，不再調用 isBookInStock() =====
              // 原子操作：檢查庫存並減少
              const bookUpdateResult = await Book.findOneAndUpdate(
                {
                  _id: mongoose.Types.ObjectId(book_id),
                  available_quantity: { $gt: 0 }
                },
                { $inc: { available_quantity: -1 } },
                {
                  new: true,
                  session: session
                }
              );

              if (!bookUpdateResult) {
                throw new Error('STOCK_OUT');
              }

              // 記錄庫存變化日誌
              console.log(`Stock decreased for borrow: ${preCheckedQuantity} -> ${bookUpdateResult.available_quantity}`);

              // 創建借閱記錄
              const mBorrow = new Borrow({
                email: req.body.email,
                user_id: req.body.user_id,
                book_id: req.body.book_id,
                issue_date: issue_date,
                return_date: return_date,
                returned: req.body.status,
                is_deleted: false
              });

              const stored = await mBorrow.save({ session });

              // 更新預約狀態
              await Reserve.findOneAndUpdate({
                book_id: req.body.book_id,
                is_deleted: false,
                user_id: req.body.user_id
              }, {
                is_deleted: true
              }, {
                useFindAndModify: false,
                session: session
              });

              return stored;
            });

            // 事務成功後處理郵件發送
            const userData = await User.findOne({ _id: user_id });
            const bookData = await Book.findOne({ _id: book_id });

            if (userData && bookData) {
              const email = req.body.email;
              const name = userData.name;
              const dateObj = req.body.return_date;
              const dateArr = dateObj.split(' ');
              const return_date = dateArr[0] + ', ' + dateArr[1] + ' ' + dateArr[2] + ' ' + dateArr[3];

              // TODO: 發送郵件
              // const mailOptions = {
              //   to: email,
              //   from: '<EMAIL>',
              //   subject: 'Book Borrowed : Enrich Culture Group',
              //   text: 'Hi! You Have Borrow this Book "' + bookData.title + '" which will return automatically on Return Date : ' + return_date
              // };
              // mailer(mailOptions);
            }

            // 正常流程清除缓存（借阅成功）
            CacheManager.clear(cacheKey, 'borrow success');
            return res.json({
              code: 200,
              data: { message: 'Book borrowed successfully' },
              message: 'Operation successful.'
            });

          } catch (error) {
            console.error('Transaction error:', error);
            // 错误情况清除缓存
            CacheManager.clear(cacheKey, 'transaction error');

            if (error.message === 'STOCK_OUT') {
              return res.json({
                code: 422,
                message: 'This book is out of stock!',
                status: false
              });
            }

            return res.json({
              code: 500,
              message: 'Internal Error in borrowing.',
              status: false
            });
          } finally {
            await session.endSession();
          }
        }
      });
    });
  } else {
    // 正常流程清除缓存（邮箱验证失败）
    CacheManager.clear(cacheKey, 'invalid email');
    return res.json({
      code: 424,
      message: 'Enter valid Email-id',
      status: false
    });
  }
});

app.post('/return', upload.none(), UserHelper, async (req, res) => {
  if (!req.body.borrow_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }

  // 使用事務確保歸還操作的原子性
  // 歸還書籍涉及多個表的更新，必須保證數據一致性
  const session = await mongoose.startSession();

  try {
    await session.withTransaction(async () => {
      // ===== 第一步：驗證借閱記錄 =====
      // 查找有效的借閱記錄（未歸還的）
      const borrowRecord = await Borrow.findOne({
        _id: req.body.borrow_id,
        returned: false
      }).session(session);

      if (!borrowRecord) {
        throw new Error('BORROW_NOT_FOUND');
      }

      const user_id = borrowRecord.user_id;
      const book_id = borrowRecord.book_id;

      // ===== 第二步：更新借閱記錄 =====
      // 標記借閱記錄為已歸還，記錄歸還時間
      // 影響表：Borrow（借閱表）
      await Borrow.findOneAndUpdate({
        _id: req.body.borrow_id
      }, {
        returned: true,
        return_date: new Date()
      }, {
        useFindAndModify: false,
        session: session
      });

      // ===== 第三步：增加書籍庫存 =====
      // 書籍歸還後，可用庫存數量 +1
      // 影響表：Book（書籍表的 available_quantity 字段）
      await BookHelper.updateAvailableStock(book_id, 'return', { session });

      // ===== 第四步：解除用戶預約限制 =====
      // 業務邏輯：當用戶有未歸還的書籍時，其預約可能被標記為 blocked
      // 歸還書籍後，需要解除該用戶的預約限制，允許其繼續預約其他書籍
      // 影響表：Reserve（預約表的 is_blocked 字段）
      await Reserve.updateOne({
        user_id: user_id,
        is_deleted: false
      }, {
        $set: {
          is_blocked: false  // 解除預約限制
        }
      }, { session });

      // ===== 第五步：處理等待中的預約 =====
      // 關鍵業務邏輯：書籍歸還後庫存增加，需要檢查是否有其他用戶在等待預約
      // 如果有預約等待，自動將預約轉換為借閱記錄
      // 影響表：Reserve（標記預約為已處理）、Borrow（創建新的借閱記錄）、Book（再次減少庫存）
      await syncReturnBooks(book_id, { session });
    });

    // 事務成功後處理郵件和文件清理（非關鍵操作，在事務外執行）
    const borrowRecord = await Borrow.findOne({ _id: req.body.borrow_id });
    if (borrowRecord) {
      const user_id = borrowRecord.user_id;
      const book_id = borrowRecord.book_id;

      // 發送郵件通知
      const userData = await User.findOne({ _id: user_id });
      const bookData = await Book.findOne({ _id: book_id });

      if (userData && bookData && userData.email) {
        // TODO: 發送歸還成功郵件
        // const mailOptions = {
        //   to: userData.email,
        //   from: '<EMAIL>',
        //   subject: 'Book Returned : Enrich Culture Group',
        //   text: 'Hi, Your Borrowed Book "' + bookData.title + '" is returned Successfully.'
        // };
        // mailer(mailOptions);
      }

      // 檢查是否需要刪除已標記刪除的書籍文件
      if (bookData && bookData.is_deleted) {
        const countsborrow = await Borrow.find({
          book_id: mongoose.Types.ObjectId(book_id),
          is_deleted: false,
          returned: false
        });

        if (countsborrow.length === 0) {
          // 刪除文件（非關鍵操作）
          const filesToDelete = [
            bookData.preview_book,
            bookData.book_pdf,
            bookData.cover_photo
          ].filter(file => file && fs.existsSync(file));

          filesToDelete.forEach(file => {
            fs.unlink(file, (err) => {
              if (err) {
                console.error(`Error deleting file ${file}:`, err);
              }
            });
          });
        }
      }
    }

    return res.json({
      code: 200,
      message: 'Operation Successful',
      status: true
    });

  } catch (error) {
    console.error('Return book error:', error);

    if (error.message === 'BORROW_NOT_FOUND') {
      return res.json({
        code: 422,
        message: 'No data found or book already returned',
        status: false
      });
    }

    return res.json({
      code: 500,
      message: 'Internal error in return book',
      status: false
    });
  } finally {
    await session.endSession();
  }
});

app.post('/deleteBook', upload.none(), UserHelper, async (req, res) => {
  if (!req.body.delete_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }
  const doc = await Borrow.findOneAndUpdate({
    _id: req.body.delete_id
  }, {
    is_deleted: true
  });

  return res.json({
    code: 200,
    data: false,
    message: 'Operation successful.'
  });
});
app.post('/alldeleteBook', upload.none(), UserHelper, async (req, res) => {
  if (!req.body.delete_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }
  const bookbulk = [];
  const arr = req.body.delete_id.split(',');
  const promises1 = arr.map(async (obj) => {
    bookbulk.push(obj);
  });

  const results1 = await Promise.all(promises1);
  const options = {
    ordered: true
  };
  const query = {
    _id: bookbulk
  };
  const data = {
    $set: {
      is_deleted: true
    }
  };
  const result = await Borrow.updateMany(query, data);
  return res.json({
    code: 200,
    data: req.body.delete_id,
    message: 'Operation successful.'
  });
});

app.post('/renew', upload.none(), UserHelper, async (req, res) => {

  if (!req.body.borrow_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }

  await Borrow.aggregate([
    {
      $match: { _id: mongoose.Types.ObjectId(req.body.borrow_id) }
    },
    {
      $lookup: {
        from: 'books',
        localField: 'book_id',
        foreignField: '_id',
        as: 'books'
      }
    },
    {
      $group: { _id: null, book_deleted: { $first: '$books.is_deleted' } }
    }
  ], async function (err, result) {
    console.log('result = ', result);
    if (result) {
      const book_deleted = result[0].book_deleted;
      console.log(book_deleted);
      if (book_deleted) {
        return res.json({
          code: 422,
          message: 'This book is no longer available with Hong Kong Public Library, You can\'t renew this book.',
          status: false
        });
      }
    }
  });


  if (!(await BookHelper.isReBorrowAllowed(req.body.borrow_id))) {
    return res.json({
      code: 422,
      message: 'Renewal of this book is not allowed',
      status: false
    });
  }

  const borrow = await Borrow.findOne({
    _id: mongoose.Types.ObjectId(req.body.borrow_id)
  });

  if (!borrow) {
    res.json({
      code: 422,
      message: 'No data found',
      status: false
    });
    return res.end();
  }

  // 同一用户同一本书只能续阅一次
  const cacheKey = 'cache_renew' + req.body.user_id + '_' + req.body.book_id;

  if (cache[cacheKey] && cache[cacheKey].expiry > Date.now()) {
    // 存在且未过期，表示处理中，直接返回
    return res.json({
      code: 418,
      message: 'Processing in progress',
      status: false
    });
  }

  // 添加到缓存并设置过期时间
  cache[cacheKey] = {
    value: cacheKey,
    expiry: Date.now() + CACHE_EXPIRY_TIME
  };

  // 清理过期的缓存
  setTimeout(() => {
    if (cache[cacheKey] && cache[cacheKey].expiry <= Date.now()) {
      delete cache[cacheKey];
    }
  }, CACHE_EXPIRY_TIME);

  const mailMessage = '';
  const issue_date = borrow.issue_date;
  const currentReturnDate = borrow.return_date;
  if (!borrow.reborrowed_once) {
    const newReturnDate = new Date(currentReturnDate);
    newReturnDate.setDate(newReturnDate.getDate() + MAX_BORROW_DAYS);
    // newReturnDate.setHours(23, 59, 0, 0);
    //newReturnDate.setDate(newReturnDate.getDate() + 7);
    const doc = await Borrow.findOneAndUpdate({
      _id: req.body.borrow_id
    }, {
      reborrowed_once: true,
      reborrow_one_date: new Date(),
      return_date: newReturnDate
    });
    User.findOne({
      _id: borrow.user_id
    }).then((userData) => {
      if (userData) {
        const email = userData.email;
        const name = userData.name;
        Book.findOne({
          _id: borrow.book_id
        }).then((bookData) => {
          if (email) {
            const dateObj = newReturnDate.toString();
            const dateArr = dateObj.split(' ');
            const return_date = dateArr[0] + ', ' + dateArr[1] + ' ' + dateArr[2] + ' ' + dateArr[3];
            // mailOptions = {
            //   to: email,
            //   from: '<EMAIL>',
            //   subject: 'Book Renewed : Enrich Culture Group',
            //   text: 'Hi, Your Borrowed Book "' + bookData.title + '" is Renewed Succesfully. Youe new Return Date is ' + return_date
            // };
            //mailer(mailOptions);
          }
        });
      }
    });

    return res.json({
      code: 200,
      data: {
        new_return_date: newReturnDate,
        issue_date: issue_date,
        reborrowed_once: true,
        reborrowed_twice: false
      },
      message: 'Operation successful.'
    });
  } else {
    if (!borrow.reborrowed_twice) {
      const newReturnDate = new Date(currentReturnDate);
      newReturnDate.setDate(newReturnDate.getDate() + MAX_BORROW_DAYS);
      // let newReturnDate = new Date(borrow.issue_date);
      // newReturnDate.setDate(newReturnDate.getDate() + 7);
      // newReturnDate.setDate(newReturnDate.getDate() + MAX_BORROW_DAYS * 3);
      // newReturnDate.setHours(23, 59, 0, 0);
      const doc = await Borrow.findOneAndUpdate({
        _id: req.body.borrow_id
      }, {
        reborrowed_twice: true,
        reborrow_two_date: new Date(),
        return_date: newReturnDate
      });
      User.findOne({
        _id: borrow.user_id
      }).then((userData) => {
        if (userData) {
          const email = userData.email;
          const name = userData.name;
          Book.findOne({
            _id: borrow.book_id
          }).then((bookData) => {
            if (email) {
              const dateObj = newReturnDate.toString();
              const dateArr = dateObj.split(' ');
              const return_date = dateArr[0] + ', ' + dateArr[1] + ' ' + dateArr[2] + ' ' + dateArr[3];
              // mailOptions = {
              //   to: email,
              //   from: '<EMAIL>',
              //   subject: 'Book Renewed : Enrich Culture Group',
              //   text: 'Hi, Your Borrowed Book "' + bookData.title + '" is Renewed Succesfully. Youe new Return Date is ' + return_date
              // };
              //mailer(mailOptions);
            }
          });
        }
      });

      return res.json({
        code: 200,
        data: {
          new_return_date: newReturnDate,
          issue_date: issue_date,
          reborrowed_once: true,
          reborrowed_twice: true
        },
        message: 'Operation successful.'
      });
    } else {
      return res.json({
        code: 200,
        message: 'You can only Renew a book 2 times',
        status: false
      });
    }
  }

});

app.post('/returndetail', upload.none(), UserHelper, async (req, res) => {
  if (!req.body.user_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }

  const borrows = await Borrow.find({
    user_id: req.body.user_id,
    is_deleted: false,
    returned: true
  }).sort({
    _id: -1
  }).populate(
    'book_id'
  );

  if (!borrows) {
    res.json({
      code: 422,
      message: 'You\'ve reached maximum limit of borrow!',
      status: false
    });
    return res.end();
  }

  const response = [];
  for (const borrowData of borrows) {
    if (borrowData.book_id) {
      const data = {
        ...borrowData._doc
      };
      data.is_reborrow_allowed = await BookHelper.isReBorrowAllowed(borrowData._id);
      response.push(data);
    }
  }

  res.json({
    code: 200,
    message: 'Operation successful.',
    data: response
  });
  return res.end();
});
app.post('/detail', upload.none(), UserHelper, async (req, res) => {
  if (!req.body.user_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }

  const borrows = await Borrow.find({
    user_id: req.body.user_id,
    is_deleted: false,
    returned: false
  }).sort({
    _id: -1
  }).populate(
    'book_id'
  );

  if (!borrows) {
    res.json({
      code: 422,
      message: 'You\'ve reached maximum limit of borrow!',
      status: false
    });
    return res.end();
  }

  const response = [];
  for (const borrowData of borrows) {
    if (borrowData.book_id) {
      const data = {
        ...borrowData._doc
      };
      data.is_reborrow_allowed = await BookHelper.isReBorrowAllowed(borrowData._id);
      response.push(data);
    }
  }

  res.json({
    code: 200,
    message: 'Operation successful.',
    data: response
  });
  return res.end();
});

// Moved to src/action/borrowAction.js

app.post('/batchReturnBorrowedBoooks', async (req, res) => {
  try {
    const condition = {
      is_deleted: false,
      returned: false,
      return_date: {
        $lt: new Date()
      }
    };

    if (req.body.user_id) {
      Object.assign(condition, { user_id: req.body.user_id });
    }

    // Process batch returns
    await batchReturnBorrowedBooks(condition);

    res.json({
      code: 200,
      message: 'Operation successful.',
      data: { ...condition }
    });
  } catch (error) {
    console.error('Error in batchReturnBorrowedBooks:', error);
    res.status(500).json({
      code: 500,
      message: 'Internal server error during batch return.',
      error: error.message
    });
  }
});


module.exports = {
  router: app,
  syncReturnBooks
};
