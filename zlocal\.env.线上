ubuntu@ip-172-31-23-228:~/staging/ebook-api$ cat .env

DEBUG_ENVIRONMENT=true
port=5000
JWT_SECRET=*>vR6D5(aI+&H-Bz>3MZj6<%OVDhS}Q+bz%p@rMF[s.1saW%Mo2zd@AhS4csgH>
DB_URL=*************************************************************************************
UPLOAD_path=http://***************:5000/uploads/
admin_base_url=http://***************:8000/
DB_USER=general-user
DB_PASSWORD=password
HKPL_CLIENT_ID=EV-JRC-001
HKPL_CLIENT_SECRET=qP4ZJJXq0deeXiT4jpoI0yrrC14QCyMT
HKPL_REDIRECT_URI=https://web-uat.joyread.club/callback
GET_ACCESS_TOKEN_API=https://sit2.slsnp.hkpl.gov.hk/porsso-a2/realms/hkpl/auth/authorization_code
GET_USER_INFO_API=https://sit2.slsnp.hkpl.gov.hk/porsso-a2/realms/hkpl/userinfo

HKPL_SW_CLIENT_ID=EV-SW-001
HKPL_SW_CLIENT_SECRET=QYfx8PD0hw9THRbZF6kWCLfC9tSzaBqU
HKPL_SW_REDIRECT_URI=https://web-uat.joyread.club/callback/sw

HKPL_TW_CLIENT_ID=EV-TW-001
HKPL_TW_CLIENT_SECRET=mkm3xi8IO4Q6DVMcJgDJ5DVmiHujvbWQ
HKPL_TW_REDIRECT_URI=https://web-uat.joyread.club/callback/tw

#SSL_PRIVATE_KEY=/etc/letsencrypt/live/projects.xcitech.in/privkey.pem
#SSL_CERTIFICATE=/etc/letsencrypt/live/projects.xcitech.in/cert.pem
#SSL_CHAIN=/etc/letsencrypt/live/projects.xcitech.in/chain.pem
#DB_URL=mongodb://139.59.66.105:27017/ebook?authSource=admin
#DB_USER=manan
#DB_PASSWORD=5CHyB1wPkrbnXjUGrrDD
#JWT_SECRET=*>vR6D5(aI+&H-Bz>3MZj6<%OVDhS}Q+bz%p@rMF[s.1saW%Mo2zd@AhS4csgH>
