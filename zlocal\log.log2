Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Reading cache set: reading_start_341787078_680af45c41db32001c2ab5f6 (expires in 3 minutes)
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Stock decreased for reading: 3 -> 2
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Stock updated for book 680af45c41db32001c2ab5f6: 3 -> 3
New reading started - Client: 341787078, Book: 680af45c41db32001c2ab5f6, Reading ID: 6886e6383ff331001d2a67f7, IP: **************
Reading record ID: 6886e6383ff331001d2a67f9
Reading cache cleared (reading start success): reading_start_341787078_680af45c41db32001c2ab5f6
Processing batch return for 1 readings
Skipping reading 6886e6383ff331001d2a67f7 - not expired yet (0.26 minutes)
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Reading cache set: reading_start_-1283692964_680af45c41db32001c2ab5f6 (expires in 3 minutes)
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock decreased for reading: 2 -> 1
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock updated for book 680af45c41db32001c2ab5f6: 2 -> 2
New reading started - Client: -1283692964, Book: 680af45c41db32001c2ab5f6, Reading ID: 6886e6533ff331001d2a6820, IP: **************
Reading record ID: 6886e6533ff331001d2a6822
Reading cache cleared (reading start success): reading_start_-1283692964_680af45c41db32001c2ab5f6
Reading return API called: 6886e6533ff331001d2a6820
Reading cache set: reading_return_6886e6533ff331001d2a6820 (expires in 5 minutes)
Returning book - Client: -1283692964, Book: 680af45c41db32001c2ab5f6, Reading ID: 6886e6533ff331001d2a6820, IP: **************
Client -1283692964 has 0 other active readings for book 680af45c41db32001c2ab5f6
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock updated for return: 1 -> 2
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock updated for book 680af45c41db32001c2ab5f6: 1 -> 1
reservations =  []
Reading cache cleared (return success): reading_return_6886e6533ff331001d2a6820
Reading book returned successfully: 6886e6533ff331001d2a6820
Reading return API called: 6886e6383ff331001d2a67f7
Reading cache set: reading_return_6886e6383ff331001d2a67f7 (expires in 5 minutes)
Returning book - Client: 341787078, Book: 680af45c41db32001c2ab5f6, Reading ID: 6886e6383ff331001d2a67f7, IP: **************
Client 341787078 has 0 other active readings for book 680af45c41db32001c2ab5f6
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock updated for return: 2 -> 3
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock updated for book 680af45c41db32001c2ab5f6: 2 -> 2
reservations =  []
Reading cache cleared (return success): reading_return_6886e6383ff331001d2a67f7
Reading book returned successfully: 6886e6383ff331001d2a67f7
Recalculating stock for book 6103fe5f18271b1fc98e4fe2, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
Recalculating stock for book 6103fe5f18271b1fc98e4fe2, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
Recalculating stock for book 6103fe5f18271b1fc98e4fe2, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6377d0d279a3ba001d1b9e63, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6103fe5f18271b1fc98e4feb, current quantity: 1/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 0, Available: 1
Recalculating stock for book 6103fe5f18271b1fc98e4fe2, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
{} filterRecords match
{} filterRecords match
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Reading cache set: reading_start_375965623_6377d0d279a3ba001d1b9e66 (expires in 3 minutes)
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Stock decreased for reading: 3 -> 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Stock updated for book 6377d0d279a3ba001d1b9e66: 3 -> 3
New reading started - Client: 375965623, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886eac03ff331001d2a69bf, IP: **************
Reading record ID: 6886eac03ff331001d2a69c1
Reading cache cleared (reading start success): reading_start_375965623_6377d0d279a3ba001d1b9e66
Processing batch return for 1 readings
Skipping reading 6886eac03ff331001d2a69bf - not expired yet (1.92 minutes)
Reading return API called: 6886eac03ff331001d2a69bf
Reading cache set: reading_return_6886eac03ff331001d2a69bf (expires in 5 minutes)
Returning book - Client: 375965623, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886eac03ff331001d2a69bf, IP: **************
Client 375965623 has 0 other active readings for book 6377d0d279a3ba001d1b9e66
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock updated for return: 2 -> 3
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock updated for book 6377d0d279a3ba001d1b9e66: 2 -> 2
reservations =  []
Reading cache cleared (return success): reading_return_6886eac03ff331001d2a69bf
Reading book returned successfully: 6886eac03ff331001d2a69bf
Recalculating stock for book 6103fe5f18271b1fc98e4feb, current quantity: 1/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 0, Available: 1
{} filterRecords match
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
authorized
authorized
authorized
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
{} filterRecords match
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Reading cache set: reading_start_-1682478900_6377d0d279a3ba001d1b9e66 (expires in 3 minutes)
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Stock decreased for reading: 3 -> 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Stock updated for book 6377d0d279a3ba001d1b9e66: 3 -> 3
New reading started - Client: -1682478900, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886efb03ff331001d2a6b41, IP: **************
Reading record ID: 6886efb03ff331001d2a6b43
Reading cache cleared (reading start success): reading_start_-1682478900_6377d0d279a3ba001d1b9e66
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
{} filterRecords match
{} filterRecords match
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Reading cache set: reading_start_375965623_6377d0d279a3ba001d1b9e66 (expires in 3 minutes)
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock decreased for reading: 2 -> 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock updated for book 6377d0d279a3ba001d1b9e66: 2 -> 2
New reading started - Client: 375965623, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886efca3ff331001d2a6bc6, IP: **************
Reading record ID: 6886efca3ff331001d2a6bc8
Reading cache cleared (reading start success): reading_start_375965623_6377d0d279a3ba001d1b9e66
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Reading cache set: reading_start_943000547_6377d0d279a3ba001d1b9e66 (expires in 3 minutes)
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock decreased for reading: 1 -> 0
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock updated for book 6377d0d279a3ba001d1b9e66: 1 -> 1
New reading started - Client: 943000547, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886efdb3ff331001d2a6beb, IP: **************
Reading record ID: 6886efdb3ff331001d2a6bed
Reading cache cleared (reading start success): reading_start_943000547_6377d0d279a3ba001d1b9e66
Reading return API called: 6886efb03ff331001d2a6b41
Reading cache set: reading_return_6886efb03ff331001d2a6b41 (expires in 5 minutes)
Returning book - Client: -1682478900, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886efb03ff331001d2a6b41, IP: **************
Client -1682478900 has 0 other active readings for book 6377d0d279a3ba001d1b9e66
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
Stock updated for return: 0 -> 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
Stock updated for book 6377d0d279a3ba001d1b9e66: 0 -> 0
reservations =  []
Reading cache cleared (return success): reading_return_6886efb03ff331001d2a6b41
Reading book returned successfully: 6886efb03ff331001d2a6b41
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Reading return API called: 6886efca3ff331001d2a6bc6
Reading cache set: reading_return_6886efca3ff331001d2a6bc6 (expires in 5 minutes)
Returning book - Client: 375965623, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886efca3ff331001d2a6bc6, IP: **************
Client 375965623 has 0 other active readings for book 6377d0d279a3ba001d1b9e66
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock updated for return: 1 -> 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock updated for book 6377d0d279a3ba001d1b9e66: 1 -> 1
reservations =  []
Reading cache cleared (return success): reading_return_6886efca3ff331001d2a6bc6
Reading book returned successfully: 6886efca3ff331001d2a6bc6
Reading return API called: 6886efdb3ff331001d2a6beb
Reading cache set: reading_return_6886efdb3ff331001d2a6beb (expires in 5 minutes)
Returning book - Client: 943000547, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886efdb3ff331001d2a6beb, IP: **************
Client 943000547 has 0 other active readings for book 6377d0d279a3ba001d1b9e66
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock updated for return: 2 -> 3
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock updated for book 6377d0d279a3ba001d1b9e66: 2 -> 2
reservations =  []
Reading cache cleared (return success): reading_return_6886efdb3ff331001d2a6beb
Reading book returned successfully: 6886efdb3ff331001d2a6beb
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Reading cache set: reading_start_943000547_6377d0d279a3ba001d1b9e66 (expires in 3 minutes)
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Stock decreased for reading: 3 -> 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Stock updated for book 6377d0d279a3ba001d1b9e66: 3 -> 3
New reading started - Client: 943000547, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886f01c3ff331001d2a6c92, IP: **************
Reading record ID: 6886f01c3ff331001d2a6c94
Reading cache cleared (reading start success): reading_start_943000547_6377d0d279a3ba001d1b9e66
Reading cache set: reading_start_375965623_6377d0d279a3ba001d1b9e66 (expires in 3 minutes)
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock decreased for reading: 2 -> 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 1, Available: 2
Stock updated for book 6377d0d279a3ba001d1b9e66: 2 -> 2
New reading started - Client: 375965623, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886f01c3ff331001d2a6ca4, IP: **************
Reading record ID: 6886f01c3ff331001d2a6ca6
Reading cache cleared (reading start success): reading_start_375965623_6377d0d279a3ba001d1b9e66
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Reading cache set: reading_start_-1682478900_6377d0d279a3ba001d1b9e66 (expires in 3 minutes)
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock decreased for reading: 1 -> 0
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock updated for book 6377d0d279a3ba001d1b9e66: 1 -> 1
New reading started - Client: -1682478900, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886f01f3ff331001d2a6cc5, IP: **************
Reading record ID: 6886f01f3ff331001d2a6cc7
Reading cache cleared (reading start success): reading_start_-1682478900_6377d0d279a3ba001d1b9e66
Processing batch return for 3 readings
Skipping reading 6886f01c3ff331001d2a6c92 - not expired yet (0.06 minutes)
Skipping reading 6886f01c3ff331001d2a6ca4 - not expired yet (0.06 minutes)
Skipping reading 6886f01f3ff331001d2a6cc5 - not expired yet (0.01 minutes)
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
{} filterRecords match
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
{} filterRecords match
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
{} filterRecords match
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
Reading return API called: 6886f01f3ff331001d2a6cc5
Reading cache set: reading_return_6886f01f3ff331001d2a6cc5 (expires in 5 minutes)
Returning book - Client: -1682478900, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886f01f3ff331001d2a6cc5, IP: **************
Client -1682478900 has 0 other active readings for book 6377d0d279a3ba001d1b9e66
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
Stock updated for return: 0 -> 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
Stock updated for book 6377d0d279a3ba001d1b9e66: 0 -> 0
reservations =  [
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f0363ff331001d2a6d05,
    user_id: {
      language: 'ch',
      offset: '-480',
      _id: 624d62b83172927a1d64a96d,
      patronid: '9708521',
      name: 'e-*************4',
      is_active: true,
      login_date: 2022-04-06T09:51:52.862Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 0,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  },
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f0443ff331001d2a6d94,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 62cbb58b003c8d7dddf0b9f9,
      patronid: '9708545',
      name: '********* 14',
      is_active: false,
      login_date: 2022-07-11T05:30:51.317Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 0,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  },
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f0603ff331001d2a6e62,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 62cd9ad0a0f0362222c3a3af,
      patronid: '9708519',
      name: 'e-*************2',
      is_active: true,
      login_date: 2022-07-12T16:01:20.523Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 0,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  },
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f06d3ff331001d2a6ec4,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 6281cea3bd89fb11958f5a40,
      patronid: '9708518',
      name: 'e-********1',
      is_active: true,
      login_date: 2022-05-16T04:10:11.731Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 0,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  }
]
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 3, Available: 0
Stock decrease failed for borrow: 当前库存为0，无法减少
预约转借阅失败，库存不足: 用户 624d62b83172927a1d64a96d, 书籍 6377d0d279a3ba001d1b9e66, 预约ID 6886f0363ff331001d2a6d05
没有库存，停止处理
Reading cache cleared (return success): reading_return_6886f01f3ff331001d2a6cc5
Reading book returned successfully: 6886f01f3ff331001d2a6cc5
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Processing batch return for 2 readings
Skipping reading 6886f01c3ff331001d2a6c92 - not expired yet (1.05 minutes)
Skipping reading 6886f01c3ff331001d2a6ca4 - not expired yet (1.04 minutes)
Reading return API called: 6886f01c3ff331001d2a6ca4
Reading cache set: reading_return_6886f01c3ff331001d2a6ca4 (expires in 5 minutes)
Returning book - Client: 375965623, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886f01c3ff331001d2a6ca4, IP: **************
Client 375965623 has 0 other active readings for book 6377d0d279a3ba001d1b9e66
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock updated for return: 1 -> 2
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock updated for book 6377d0d279a3ba001d1b9e66: 1 -> 1
reservations =  [
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f0363ff331001d2a6d05,
    user_id: {
      language: 'ch',
      offset: '-480',
      _id: 624d62b83172927a1d64a96d,
      patronid: '9708521',
      name: 'e-*************4',
      is_active: true,
      login_date: 2022-04-06T09:51:52.862Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 1,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  },
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f0443ff331001d2a6d94,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 62cbb58b003c8d7dddf0b9f9,
      patronid: '9708545',
      name: '********* 14',
      is_active: false,
      login_date: 2022-07-11T05:30:51.317Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 1,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  },
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f0603ff331001d2a6e62,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 62cd9ad0a0f0362222c3a3af,
      patronid: '9708519',
      name: 'e-*************2',
      is_active: true,
      login_date: 2022-07-12T16:01:20.523Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 1,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  },
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f06d3ff331001d2a6ec4,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 6281cea3bd89fb11958f5a40,
      patronid: '9708518',
      name: 'e-********1',
      is_active: true,
      login_date: 2022-05-16T04:10:11.731Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 1,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  }
]
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock decreased for borrow: 1 -> 1
预约转借阅成功: 用户 624d62b83172927a1d64a96d, 书籍 6377d0d279a3ba001d1b9e66, 预约ID 6886f0363ff331001d2a6d05
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 2, Available: 1
Stock decreased for borrow: 1 -> 0
预约转借阅成功: 用户 62cbb58b003c8d7dddf0b9f9, 书籍 6377d0d279a3ba001d1b9e66, 预约ID 6886f0443ff331001d2a6d94
没有库存，停止处理
Reading cache cleared (return success): reading_return_6886f01c3ff331001d2a6ca4
Reading book returned successfully: 6886f01c3ff331001d2a6ca4
An e-mail has been <NAME_EMAIL>
An e-mail has been <NAME_EMAIL>
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 1, Available: 0
Processing batch return for 1 readings
Skipping reading 6886f01c3ff331001d2a6c92 - not expired yet (0.05 minutes)
Reading return API called: 6886f01c3ff331001d2a6c92
Reading cache set: reading_return_6886f01c3ff331001d2a6c92 (expires in 5 minutes)
Returning book - Client: 943000547, Book: 6377d0d279a3ba001d1b9e66, Reading ID: 6886f01c3ff331001d2a6c92, IP: **************
Client 943000547 has 0 other active readings for book 6377d0d279a3ba001d1b9e66
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 1, Available: 0
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 1, Available: 0
Stock updated for return: 0 -> 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 1, Available: 0
Stock updated for book 6377d0d279a3ba001d1b9e66: 0 -> 0
reservations =  [
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f0603ff331001d2a6e62,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 62cd9ad0a0f0362222c3a3af,
      patronid: '9708519',
      name: 'e-*************2',
      is_active: true,
      login_date: 2022-07-12T16:01:20.523Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 0,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  },
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f06d3ff331001d2a6ec4,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 6281cea3bd89fb11958f5a40,
      patronid: '9708518',
      name: 'e-********1',
      is_active: true,
      login_date: 2022-05-16T04:10:11.731Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 0,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  }
]
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 1, Available: 0
Stock decrease failed for borrow: 当前库存为0，无法减少
预约转借阅失败，库存不足: 用户 62cd9ad0a0f0362222c3a3af, 书籍 6377d0d279a3ba001d1b9e66, 预约ID 6886f0603ff331001d2a6e62
没有库存，停止处理
Reading cache cleared (return success): reading_return_6886f01c3ff331001d2a6c92
Reading book returned successfully: 6886f01c3ff331001d2a6c92
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 0, Available: 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 0, Available: 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 0, Available: 1
Stock updated for return: 1 -> 2
reservations =  [
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f0603ff331001d2a6e62,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 62cd9ad0a0f0362222c3a3af,
      patronid: '9708519',
      name: 'e-*************2',
      is_active: true,
      login_date: 2022-07-12T16:01:20.523Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 1,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  },
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886f06d3ff331001d2a6ec4,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 6281cea3bd89fb11958f5a40,
      patronid: '9708518',
      name: 'e-********1',
      is_active: true,
      login_date: 2022-05-16T04:10:11.731Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6377d0d279a3ba001d1b9e66,
      title: '千萬富翁致富學問',
      excerpt: '致富教練龔成投資有道，由普通打工仔成為80後千萬富翁，多年來吸引無數粉絲追隨他的致富心得。本書整理具代表性的152封粉絲來信及他的回應，有投資初哥、迷惘打工仔、專業人士、退休人士、債台高築人士，甚至中學生，問題遍佈股票分析、物業、基金、保險產品、理財疑慮、資產配置等。\r\n' +
        '\r\n' +
        '這些粉絲面對的財務難題，不難在我們身上看到。每一封來信有如一堂理財課，讓讀者以第一身角度明白致富的核心法則。',
      stock_quantity: 3,
      available_quantity: 1,
      author: '龔成',
      cover_photo: 'uploads/9789888599738.jpg',
      book_pdf: 'uploads/9789888599738.pdf',
      preview_book: 'uploads/9789888599738_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.810Z,
      publishingGroup: '天窗',
      imprints: '天窗出版',
      isbn_no: '9789888599738',
      publish_date: 2021-12-01T00:00:00.000Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  }
]
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 0, Available: 1
Stock decreased for borrow: 1 -> 1
预约转借阅成功: 用户 62cd9ad0a0f0362222c3a3af, 书籍 6377d0d279a3ba001d1b9e66, 预约ID 6886f0603ff331001d2a6e62
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 0, Available: 1
Stock decreased for borrow: 1 -> 0
预约转借阅成功: 用户 6281cea3bd89fb11958f5a40, 书籍 6377d0d279a3ba001d1b9e66, 预约ID 6886f06d3ff331001d2a6ec4
An e-mail has been <NAME_EMAIL>
An e-mail has been <NAME_EMAIL>
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 3, Reading: 0, Available: 0
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 0/3
Stock calculation - Total: 3, Borrowed: 3, Reading: 0, Available: 0
Stock updated for return: 0 -> 1
reservations =  []
Return book error: Error: BORROW_NOT_FOUND
    at /home/<USER>/app/Routes/Borrow.js:406:15
    at runMicrotasks (<anonymous>)
    at processTicksAndRejections (node:internal/process/task_queues:96:5)
    at async /home/<USER>/app/Routes/Borrow.js:397:5
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 0, Available: 1
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 1/3
Stock calculation - Total: 3, Borrowed: 2, Reading: 0, Available: 1
Stock updated for return: 1 -> 2
reservations =  []
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 2/3
Stock calculation - Total: 3, Borrowed: 1, Reading: 0, Available: 2
Stock updated for return: 2 -> 3
reservations =  []
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6377d0d279a3ba001d1b9e66, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6103fe5f18271b1fc98e4fe2, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
{} filterRecords match
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
Recalculating stock for book 6103fe5f18271b1fc98e4e56, current quantity: 1/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 0, Available: 1
Reading cache set: reading_start_375965623_6103fe5f18271b1fc98e4e56 (expires in 3 minutes)
Recalculating stock for book 6103fe5f18271b1fc98e4e56, current quantity: 1/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 0, Available: 1
Recalculating stock for book 6103fe5f18271b1fc98e4e56, current quantity: 1/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 0, Available: 1
Recalculating stock for book 6103fe5f18271b1fc98e4e56, current quantity: 1/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 0, Available: 1
Stock decreased for reading: 1 -> 0
Recalculating stock for book 6103fe5f18271b1fc98e4e56, current quantity: 1/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 0, Available: 1
Stock updated for book 6103fe5f18271b1fc98e4e56: 1 -> 1
New reading started - Client: 375965623, Book: 6103fe5f18271b1fc98e4e56, Reading ID: 6886fa713ff331001d2a77c1, IP: **************
Reading record ID: 6886fa713ff331001d2a77c3
Reading cache cleared (reading start success): reading_start_375965623_6103fe5f18271b1fc98e4e56
{} filterRecords match
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
Recalculating stock for book 6103fe5f18271b1fc98e4e56, current quantity: 0/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 1, Available: 0
Reading return API called: 6886fa713ff331001d2a77c1
Reading cache set: reading_return_6886fa713ff331001d2a77c1 (expires in 5 minutes)
Returning book - Client: 375965623, Book: 6103fe5f18271b1fc98e4e56, Reading ID: 6886fa713ff331001d2a77c1, IP: **************
Client 375965623 has 0 other active readings for book 6103fe5f18271b1fc98e4e56
Recalculating stock for book 6103fe5f18271b1fc98e4e56, current quantity: 0/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 1, Available: 0
Recalculating stock for book 6103fe5f18271b1fc98e4e56, current quantity: 0/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 1, Available: 0
Stock updated for return: 0 -> 1
Recalculating stock for book 6103fe5f18271b1fc98e4e56, current quantity: 0/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 1, Available: 0
Stock updated for book 6103fe5f18271b1fc98e4e56: 0 -> 0
reservations =  [
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886fa833ff331001d2a7801,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 62cbb58b003c8d7dddf0b9f9,
      patronid: '9708545',
      name: '********* 14',
      is_active: false,
      login_date: 2022-07-11T05:30:51.317Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: null,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6103fe5f18271b1fc98e4e56,
      title: '三十而立',
      excerpt: '一群三十出頭的專業人士組成「三十會」，希望能匯聚一班青年專業人士之論壇組織，深入認識當前香港所面臨之各種問題，積極參與及回饋社會。這次他們走出辦公室，以文字、以行動，為糾纏在政客與官僚中的香港，出謀獻策，重新創造香港，只因堅信，香港未來一棒，遲早會交到他們一代手中。',
      stock_quantity: 1,
      available_quantity: 0,
      author: '三十會',
      cover_photo: 'uploads/9789889832872.jpg',
      book_pdf: 'uploads/9789889832872.pdf',
      preview_book: 'uploads/9789889832872_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      added_at: 2022-11-18T18:37:03.756Z,
      isbn_no: '9789889832872',
      publish_date: 2006-02-01T00:00:00.000Z,
      __v: 0,
      collection: 'JYG',
      imprints: '天窗出版',
      publishingGroup: '天窗'
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  }
]
Recalculating stock for book 6103fe5f18271b1fc98e4e56, current quantity: 0/1
Stock calculation - Total: 1, Borrowed: 0, Reading: 1, Available: 0
Stock decrease failed for borrow: 当前库存为0，无法减少
预约转借阅失败，库存不足: 用户 62cbb58b003c8d7dddf0b9f9, 书籍 6103fe5f18271b1fc98e4e56, 预约ID 6886fa833ff331001d2a7801
没有库存，停止处理
Reading cache cleared (return success): reading_return_6886fa713ff331001d2a77c1
Reading book returned successfully: 6886fa713ff331001d2a77c1
{} filterRecords match
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
Reading cache set: reading_start_375965623_6491133928e726001d3062d9 (expires in 3 minutes)
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
Stock decreased for reading: 2 -> 1
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
Stock updated for book 6491133928e726001d3062d9: 2 -> 2
New reading started - Client: 375965623, Book: 6491133928e726001d3062d9, Reading ID: 6886fae43ff331001d2a79d5, IP: **************
Reading record ID: 6886fae43ff331001d2a79d7
Reading cache cleared (reading start success): reading_start_375965623_6491133928e726001d3062d9
{} filterRecords match
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 1, Available: 1
Reading cache set: reading_start_-1949549162_6491133928e726001d3062d9 (expires in 3 minutes)
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 1, Available: 1
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 1, Available: 1
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 1, Available: 1
Stock decreased for reading: 1 -> 0
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 1, Available: 1
Stock updated for book 6491133928e726001d3062d9: 1 -> 1
New reading started - Client: -1949549162, Book: 6491133928e726001d3062d9, Reading ID: 6886fb1b3ff331001d2a7a2a, IP: **************
Reading record ID: 6886fb1b3ff331001d2a7a2c
Reading cache cleared (reading start success): reading_start_-1949549162_6491133928e726001d3062d9
{ '$and': [ { '$or': [Array] }, { '$or': [Array] } ] } filterRecords match
{} filterRecords match
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 0/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 2, Available: 0
Reading return API called: 6886fae43ff331001d2a79d5
Reading cache set: reading_return_6886fae43ff331001d2a79d5 (expires in 5 minutes)
Returning book - Client: 375965623, Book: 6491133928e726001d3062d9, Reading ID: 6886fae43ff331001d2a79d5, IP: **************
Client 375965623 has 0 other active readings for book 6491133928e726001d3062d9
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 0/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 2, Available: 0
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 0/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 2, Available: 0
Stock updated for return: 0 -> 1
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 0/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 2, Available: 0
Stock updated for book 6491133928e726001d3062d9: 0 -> 0
reservations =  [
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886fb2b3ff331001d2a7a6b,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 62cbb58b003c8d7dddf0b9f9,
      patronid: '9708545',
      name: '********* 14',
      is_active: false,
      login_date: 2022-07-11T05:30:51.317Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6491133928e726001d3062d9,
      title: '港樓變薪術',
      excerpt: '面對移民潮疊加人口老化的兩大趨勢，如何用盡「港樓」價值，滿足一個 家庭三代人的需要？移民的，如移往高稅國家，不妨保留港樓出租，為回流「留一手」，識部署不單不怕稅，甚至可以製造出「節稅現金流」；留港的，亦可憑自住物業耍出「變薪組合拳」，套現套息，過更豐盛的退「優」生活！\r\n' +
        '\r\n' +
        '「退休理財」專家李澄幸夥拍「港樓」專家陳智鑾，從港人最關心的「移民潮」說起，將「物業 x 理財」規劃深度結合，以大量「貼地」案例，多角度剖析當今「港樓」價值並傳授「變薪」部署，滿足不同家庭的理財需求。\r\n' +
        '\r\n' +
        '對有意移民的業主，作者建議藉三大原則決定港樓去留，亦分享如何妥善遙距管理物業，以及如何以加按「變薪」，同時慳盡入息稅、資產增值稅以至遺產稅！\r\n' +
        '\r\n' +
        '另外，對留港發展以至退休的業主，作者傳授多套「變薪組合拳」，結合安老按揭、加按套息、各種理財工具等，教讀者自製「長糧」，憑港樓「變薪」現金流，亦可用盡公共政策下之福利，每月輕鬆賺取穩定的被動收入，優游自在！',
      stock_quantity: 2,
      available_quantity: 0,
      publishingGroup: '天窗',
      imprints: '天窗出版社',
      author: '李澄幸, 陳智鑾',
      isbn_no: '9789888599950',
      cover_photo: 'uploads/9789888599950.jpg',
      book_pdf: 'uploads/9789888599950.pdf',
      preview_book: 'uploads/9789888599950_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      publish_date: 2023-04-30T16:00:00.000Z,
      added_at: 2023-06-20T02:47:21.987Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  }
]
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 0/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 2, Available: 0
Stock decrease failed for borrow: 当前库存为0，无法减少
预约转借阅失败，库存不足: 用户 62cbb58b003c8d7dddf0b9f9, 书籍 6491133928e726001d3062d9, 预约ID 6886fb2b3ff331001d2a7a6b
没有库存，停止处理
Reading cache cleared (return success): reading_return_6886fae43ff331001d2a79d5
Reading book returned successfully: 6886fae43ff331001d2a79d5
Reading return API called: 6886fb1b3ff331001d2a7a2a
Reading cache set: reading_return_6886fb1b3ff331001d2a7a2a (expires in 5 minutes)
Returning book - Client: -1949549162, Book: 6491133928e726001d3062d9, Reading ID: 6886fb1b3ff331001d2a7a2a, IP: **************
Client -1949549162 has 0 other active readings for book 6491133928e726001d3062d9
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 1, Available: 1
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 1, Available: 1
Stock updated for return: 1 -> 2
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 1, Available: 1
Stock updated for book 6491133928e726001d3062d9: 1 -> 1
reservations =  [
  {
    is_deleted: false,
    is_mailed: false,
    is_blocked: false,
    _id: 6886fb2b3ff331001d2a7a6b,
    user_id: {
      language: 'en',
      offset: '-480',
      _id: 62cbb58b003c8d7dddf0b9f9,
      patronid: '9708545',
      name: '********* 14',
      is_active: false,
      login_date: 2022-07-11T05:30:51.317Z,
      email: '<EMAIL>',
      __v: 0
    },
    email: '<EMAIL>',
    book_id: {
      category_id: [Array],
      book_recomm: false,
      is_deleted: false,
      collection_type: 'JYG',
      _id: 6491133928e726001d3062d9,
      title: '港樓變薪術',
      excerpt: '面對移民潮疊加人口老化的兩大趨勢，如何用盡「港樓」價值，滿足一個 家庭三代人的需要？移民的，如移往高稅國家，不妨保留港樓出租，為回流「留一手」，識部署不單不怕稅，甚至可以製造出「節稅現金流」；留港的，亦可憑自住物業耍出「變薪組合拳」，套現套息，過更豐盛的退「優」生活！\r\n' +
        '\r\n' +
        '「退休理財」專家李澄幸夥拍「港樓」專家陳智鑾，從港人最關心的「移民潮」說起，將「物業 x 理財」規劃深度結合，以大量「貼地」案例，多角度剖析當今「港樓」價值並傳授「變薪」部署，滿足不同家庭的理財需求。\r\n' +
        '\r\n' +
        '對有意移民的業主，作者建議藉三大原則決定港樓去留，亦分享如何妥善遙距管理物業，以及如何以加按「變薪」，同時慳盡入息稅、資產增值稅以至遺產稅！\r\n' +
        '\r\n' +
        '另外，對留港發展以至退休的業主，作者傳授多套「變薪組合拳」，結合安老按揭、加按套息、各種理財工具等，教讀者自製「長糧」，憑港樓「變薪」現金流，亦可用盡公共政策下之福利，每月輕鬆賺取穩定的被動收入，優游自在！',
      stock_quantity: 2,
      available_quantity: 1,
      publishingGroup: '天窗',
      imprints: '天窗出版社',
      author: '李澄幸, 陳智鑾',
      isbn_no: '9789888599950',
      cover_photo: 'uploads/9789888599950.jpg',
      book_pdf: 'uploads/9789888599950.pdf',
      preview_book: 'uploads/9789888599950_preview.pdf',
      added_by: 5fc8ad0d7b9f2481556be495,
      publish_date: 2023-04-30T16:00:00.000Z,
      added_at: 2023-06-20T02:47:21.987Z,
      __v: 0
    },
    reserve_date: 2025-07-28T15:59:00.000Z,
    status: true,
    __v: 0
  }
]
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 1, Available: 1
Stock decreased for borrow: 1 -> 1
预约转借阅成功: 用户 62cbb58b003c8d7dddf0b9f9, 书籍 6491133928e726001d3062d9, 预约ID 6886fb2b3ff331001d2a7a6b
Reading cache cleared (return success): reading_return_6886fb1b3ff331001d2a7a2a
Reading book returned successfully: 6886fb1b3ff331001d2a7a2a
An e-mail has been <NAME_EMAIL>
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 1, Reading: 0, Available: 1
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 1, Reading: 0, Available: 1
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 1, Reading: 0, Available: 1
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 1, Reading: 0, Available: 1
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 1, Reading: 0, Available: 1
Recalculating stock for book 6491133928e726001d3062d9, current quantity: 1/2
Stock calculation - Total: 2, Borrowed: 1, Reading: 0, Available: 1
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
Recalculating stock for book 6876178641c2c2001daafd13, current quantity: 2/2
Stock calculation - Total: 2, Borrowed: 0, Reading: 0, Available: 2
Recalculating stock for book 680af45c41db32001c2ab5f6, current quantity: 3/3
Stock calculation - Total: 3, Borrowed: 0, Reading: 0, Available: 3
authorized
