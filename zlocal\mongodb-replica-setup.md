# MongoDB 副本集配置指南

## 方案A：单机副本集（0成本，支持事务）

### 1. 停止当前 MongoDB
```bash
sudo systemctl stop mongod
```

### 2. 备份当前数据（重要！）
```bash
sudo cp -r /var/lib/mongodb /var/lib/mongodb_backup
```

### 3. 修改 MongoDB 配置文件
```bash
sudo nano /etc/mongod.conf
```

添加副本集配置：
```yaml
# /etc/mongod.conf
storage:
  dbPath: /var/lib/mongodb
  journal:
    enabled: true

systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

net:
  port: 27017
  bindIp: 127.0.0.1,***************  # 添加你的服务器IP

# 添加副本集配置
replication:
  replSetName: "rs0"
```

### 4. 重启 MongoDB
```bash
sudo systemctl start mongod
sudo systemctl status mongod
```

### 5. 初始化副本集
```bash
mongo --host ***************:27017
```

在 MongoDB shell 中执行：
```javascript
// 初始化副本集
rs.initiate({
  _id: "rs0",
  members: [
    { _id: 0, host: "***************:27017" }
  ]
})

// 检查状态
rs.status()

// 确认是主节点
rs.isMaster()
```

### 6. 更新应用连接字符串
```javascript
// 原来的连接
DB_URL=***********************************************************

// 新的连接（副本集）
DB_URL=**************************************************************************
```

### 7. 测试事务功能
```javascript
// 测试代码
const session = await mongoose.startSession();
try {
  await session.withTransaction(async () => {
    // 你的事务代码
    console.log('事务测试成功！');
  });
} finally {
  await session.endSession();
}
```

## 方案B：经济型副本集（小额成本，高可用）

### 1. 购买一个最小的云服务器实例
- **配置**：1核512M内存，10G硬盘
- **成本**：约$5-10/月
- **用途**：作为仲裁节点

### 2. 在仲裁节点安装 MongoDB
```bash
# 在新服务器上
sudo apt update
sudo apt install -y mongodb
```

### 3. 配置三节点副本集
```javascript
rs.initiate({
  _id: "rs0",
  members: [
    { _id: 0, host: "***************:27017" },           // 主节点
    { _id: 1, host: "新服务器IP:27017" },                 // 从节点
    { _id: 2, host: "仲裁节点IP:27017", arbiterOnly: true } // 仲裁节点
  ]
})
```

## 验证配置成功

### 1. 检查副本集状态
```javascript
rs.status()
```

### 2. 测试事务
```javascript
const session = await mongoose.startSession();
await session.withTransaction(async () => {
  console.log('事务功能正常！');
});
```

### 3. 更新 .env 文件
```bash
# 更新连接字符串
DB_URL=**************************************************************************
```

## 回滚方案（如果出问题）

### 1. 恢复原配置
```bash
sudo systemctl stop mongod
sudo cp /etc/mongod.conf.backup /etc/mongod.conf
sudo systemctl start mongod
```

### 2. 恢复数据
```bash
sudo rm -rf /var/lib/mongodb
sudo cp -r /var/lib/mongodb_backup /var/lib/mongodb
sudo chown -R mongodb:mongodb /var/lib/mongodb
```

## 注意事项

1. **备份重要**：配置前一定要备份数据
2. **防火墙**：确保 27017 端口在服务器间可访问
3. **网络延迟**：副本集节点间网络延迟要低
4. **监控**：配置后监控副本集健康状态

## 成本总结

- **方案A（单机副本集）**：0额外成本
- **方案B（经济副本集）**：约$5-10/月
- **收益**：完整的事务支持，数据一致性保障
