const mongoose = require('mongoose');
const Book = require('../Model/Book');
const Borrow = require('../Model/Borrow');
const Reserve = require('../Model/Reserve');
const User = require('../Model/User');
const BookHelper = require('../Helper/BookHelper');
const { syncReturnBooks } = require('../src/action/bookActions');

// 加载环境变量
require('dotenv').config();

// 连接数据库
mongoose.connect(process.env.DB_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function testWithoutTransaction() {
  console.log('=== 测试不使用事务的预约转借阅场景 ===');

  try {
    // 查找一本有库存的书
    const book = await Book.findOne({
      available_quantity: { $gte: 3 },
      stock_quantity: { $gte: 3 }
    });

    if (!book) {
      console.log('没有找到有足够库存的书籍');
      return;
    }

    // 设置测试环境：3本总库存，3本可用库存
    await Book.findByIdAndUpdate(book._id, {
      stock_quantity: 3,
      available_quantity: 3
    });

    console.log(`测试书籍: ${book.title}`);
    console.log('设置库存: 3/3');

    // 获取4个测试用户
    const testUsers = await User.find({ email: { $ne: '' } }).limit(4);
    if (testUsers.length < 4) {
      console.log('没有找到足够的测试用户');
      return;
    }

    console.log(`找到 ${testUsers.length} 个测试用户`);

    // 清理之前的测试数据
    await Reserve.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });
    await Borrow.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });

    // 创建4个预约
    const reservations = [];
    for (let i = 0; i < 4; i++) {
      const user = testUsers[i];
      const reserve = new Reserve({
        user_id: user._id,
        book_id: book._id,
        email: user.email,
        reserve_date: new Date(),
        status: true,
        is_mailed: false,
        is_deleted: false,
        is_blocked: false
      });
      await reserve.save();
      reservations.push(reserve);
      console.log(`用户${i + 1} (${user.email}) 创建预约`);
    }

    console.log('\n创建了 4 个预约，当前可用库存为 3');
    console.log('期望结果：用户1-3成功借阅，用户4保持预约状态');

    // 执行 syncReturnBooks 函数（不使用事务）
    console.log('\n=== 开始执行预约转借阅（不使用事务）===');

    try {
      await syncReturnBooks(book._id.toString());
    } catch (error) {
      console.log('执行完成，可能有错误:', error.message);
    }

    // 检查结果
    const finalBook = await Book.findById(book._id);
    const borrowRecords = await Borrow.find({
      book_id: book._id,
      returned: false,
      user_id: { $in: testUsers.map(u => u._id) }
    }).populate('user_id');

    const processedReserves = await Reserve.find({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) },
      is_deleted: true
    });

    const remainingReserves = await Reserve.find({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) },
      is_deleted: false
    });

    console.log('\n=== 测试结果 ===');
    console.log(`最终库存: ${finalBook.available_quantity}/${finalBook.stock_quantity}`);
    console.log(`成功借阅用户数: ${borrowRecords.length}`);
    console.log(`已处理预约数: ${processedReserves.length}`);
    console.log(`剩余预约数: ${remainingReserves.length}`);

    if (borrowRecords.length > 0) {
      console.log('\n成功借阅的用户:');
      borrowRecords.forEach((borrow, index) => {
        console.log(`  用户${index + 1}: ${borrow.user_id.email}`);
      });
    }

    if (remainingReserves.length > 0) {
      console.log('\n仍在等待的用户:');
      remainingReserves.forEach((reserve, index) => {
        console.log(`  用户${index + 1}: ${reserve.email}`);
      });
    }

    // 验证结果
    console.log('\n=== 验证结果 ===');

    // 检查是否正确处理了库存
    const expectedAvailable = 3 - borrowRecords.length;
    if (finalBook.available_quantity === expectedAvailable) {
      console.log('✅ 库存数据一致性检查通过');
    } else {
      console.log(`❌ 库存数据不一致: 期望 ${expectedAvailable}, 实际 ${finalBook.available_quantity}`);
    }

    // 检查是否没有超额借阅
    if (borrowRecords.length <= 3) {
      console.log('✅ 借阅数量正确（不超过库存）');
    } else {
      console.log('❌ 借阅数量错误（超过库存）');
    }

    // 检查是否有用户成功借阅
    if (borrowRecords.length > 0) {
      console.log('✅ 成功处理了部分预约转借阅');
    } else {
      console.log('⚠️  没有处理任何预约转借阅');
    }

    // 检查数据一致性：借阅记录数应该等于已处理预约数
    if (borrowRecords.length === processedReserves.length) {
      console.log('✅ 借阅记录与已处理预约数一致');
    } else {
      console.log(`❌ 数据不一致: 借阅记录 ${borrowRecords.length}, 已处理预约 ${processedReserves.length}`);
    }

    // 清理测试数据
    await Borrow.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });
    await Reserve.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });

    // 恢复原始库存
    await Book.findByIdAndUpdate(book._id, {
      available_quantity: book.stock_quantity
    });

    console.log('\n测试数据已清理');

  } catch (error) {
    console.error('测试过程中出错:', error);
  } finally {
    await mongoose.connection.close();
  }
}

// 运行测试
testWithoutTransaction();
