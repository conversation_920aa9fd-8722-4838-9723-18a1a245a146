{"name": "ebook", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:check": "eslint --print-config . | grep 'no-undef'", "precommit": "npm run lint", "test:reservation": "node test/run-test.js", "test:check": "node test/run-test.js --check", "test:create-reserves": "node test/run-test.js --create-reserves"}, "author": "", "license": "ISC", "dependencies": {"@pdftron/pdfnet-node": "^10.0.0", "app-root-path": "^3.0.0", "async": "^3.2.6", "axios": "^0.16.2", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "cors": "^2.8.5", "dotenv": "^8.2.0", "ebook-convert": "^2.0.1", "epub": "^1.2.1", "express": "^4.17.1", "express-device": "^0.4.2", "express-ip": "^1.0.4", "jsonwebtoken": "^8.5.1", "jszip": "^3.6.0", "lodash": "^4.17.21", "moment": "^2.27.0", "moment-timezone": "^0.5.40", "mongoose": "^5.9.21", "multer": "^1.4.2", "node-cron": "^2.0.3", "nodemailer": "^6.4.11", "nodemon": "^2.0.19", "ramda": "^0.28.0", "request": "^2.88.2", "saslprep": "^1.0.3", "swagger-jsdoc": "^4.0.0", "swagger-ui-express": "^4.1.4", "uuid": "^8.2.0"}, "devDependencies": {"eslint": "^8.57.0"}, "config": {"optionalDependencies": false}}