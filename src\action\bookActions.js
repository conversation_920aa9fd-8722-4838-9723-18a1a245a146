const mongoose = require('mongoose');
const Reserve = require('../../Model/Reserve');
const Book = require('../../Model/Book');
const Borrow = require('../../Model/Borrow');
const BookHelper = require('../../Helper/BookHelper');
const { sendBlockedMail, sendBorrowMail } = require('./emailAction');
const { MAX_BORROW_BOOK_NUM, MAX_BORROW_DAYS } = require('../../consts');

function calEndOfDay(utcDate = new Date(), days = 14, offsetInMinutes = -480) {
  // 1. 从 UTC 时间获取时间戳（毫秒）
  const utcTimeMs = utcDate.getTime();

  // 2. 计算该时区下的本地时间戳
  const localDate = new Date(utcTimeMs + offsetInMinutes * 60000);

  // 3. 计算目标日期（本地时间 + days 天）
  const targetLocal = new Date(localDate);
  targetLocal.setDate(localDate.getDate() + days);
  targetLocal.setHours(23, 59, 0, 0);

  const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

  // 4. 获取本地时间字符串
  const localEnd = `${targetLocal.getDate()}-${targetLocal.getMonth() + 1}-${targetLocal.getFullYear()} (${weekDays[targetLocal.getDay()]})`;

  // 5. 转为 UTC 时间（用于存储）
  const utcEnd = new Date(targetLocal.getTime() - offsetInMinutes * 60000);

  return {
    localEnd,  // 本地结束时间字符串
    utcEnd    // 对应的 UTC 时间（可存库）
  };
}

/**
 * 同步处理书籍归还后的预约转借阅流程
 *
 * 业务流程说明：
 * 1. 用户A归还书籍 → 书籍库存 +1 (在调用此函数前已完成)
 * 2. 系统检查是否有其他用户在等待预约此书
 * 3. 如果有预约等待，按预约时间顺序自动转换为借阅
 * 4. 更新相关表：预约表（标记已处理）、借阅表（创建新记录）、书籍表（再次减少库存）
 *
 * 涉及的数据表更新：
 * - Reserve表：is_mailed=true, is_deleted=true, mail_date=当前时间 (标记预约已处理)
 * - Borrow表：创建新的借阅记录 (用户可以取书)
 * - Book表：available_quantity 再次减少 (通过 updateAvailableStock)
 *
 * 为什么还书跟用户预约有关系：
 * - 图书馆的书籍数量有限，当所有副本都被借出时，其他用户只能预约等待
 * - 当有人归还书籍时，系统自动将等待中的预约转换为可借阅状态
 * - 这样确保了预约用户能够及时获得书籍，提高了图书馆的服务效率
 *
 * @param {string} book_id - 书籍ID
 * @param {Object} [options] - 选项
 * @param {Object} [options.session] - Mongoose 会话（用于事务）
 */
async function syncReturnBooks(book_id, { session } = {}) {
  // ===== 第一步：参数验证 =====
  if (!book_id) {
    console.log('syncReturnBooks: book_id is required');
    return;
  }

  // ===== 事務前預檢查：使用 isBookInStock() 確認是否有庫存可處理預約 =====
  const preCheckedQuantity = await BookHelper.isBookInStock(book_id);
  if (preCheckedQuantity <= 0) {
    console.log('事務前檢查：沒有庫存，跳過預約處理');
    return;
  }

  // ===== 第二步：计算过期时间 =====
  // 预约超过30天未处理的将被清理
  const today = new Date();
  const numberOfDaysToAddTo = 30;
  today.setDate(today.getDate() - numberOfDaysToAddTo);

  // ===== 第三步：查找等待中的预约 =====
  // 获取该书籍的所有有效预约，按预约时间排序（先预约先处理，保证公平性）
  // 重要：必须在事务中查询，确保数据一致性
  const reservations = await Reserve.find({
    book_id: mongoose.Types.ObjectId(book_id),
    is_deleted: false  // 只查找未删除的预约
  }).populate('user_id').populate('book_id').sort({ reserve_date: 1 }).session(session); // 按预约时间升序，使用事务会话

  console.log('reservations = ', reservations);

  if (reservations.length === 0) {
    return; // 无预约数据，直接返回
  }

  // ===== 第四步：获取书籍信息 =====
  const book = await Book.findOne({ _id: mongoose.Types.ObjectId(book_id) });
  if (!book) {
    console.log('Book not found:', book_id);
    return;
  }

  // ===== 第五步：按顺序处理每个预约 =====
  // 重要：必须按预约时间顺序处理，确保公平性
  for (const reserve of reservations) {
    const reserveDate = new Date(reserve.reserve_date);

    // ===== 5.1 清理过期预约 =====
    // 预约超过30天或邮件发送超过30天的预约将被删除
    if (reserveDate < today || (reserve.is_mailed && new Date(reserve.mail_date) < today)) {
      await Reserve.findByIdAndUpdate(
        reserve._id,
        { is_deleted: true },  // 标记为已删除
        {
          useFindAndModify: false,
          ...(session && { session })
        }
      );
      continue; // 跳过此预约，处理下一个
    }

    // ===== 5.2 检查当前库存状态 =====
    // 在事务中重新检查库存，防止并发情况下的数据不一致
    const currentBook = await Book.findOne({
      _id: mongoose.Types.ObjectId(book_id)
    }, null, { ...(session && { session }) });

    if (!currentBook || currentBook.available_quantity <= 0) {
      console.log('没有库存，停止处理');
      break; // 库存不足时停止处理后续预约
    }

    // ===== 5.3 跳过已处理或被阻止的预约 =====
    if (reserve.is_mailed || reserve.is_blocked) {
      continue; // 已邮件通知或被阻止的预约跳过
    }

    // ===== 5.4 验证用户信息 =====
    const user = reserve.user_id;
    if (!user) {
      console.log('User not found for reserve:', reserve._id);
      continue; // 用户不存在，跳过此预约
    }

    // ===== 5.5 检查用户借阅限制 =====
    // 检查用户当前借阅数量是否超过限制
    const countsBorrowed = await BookHelper.totalBookBorrowedInPresent(user._id, book.collection_type);
    if (countsBorrowed >= MAX_BORROW_BOOK_NUM) {
      // 用户已达到借阅上限，标记预约为被阻止状态
      // 影响表：Reserve（预约表的 is_blocked 字段）
      await Reserve.findByIdAndUpdate(reserve._id, { is_blocked: true }, {
        useFindAndModify: false,
        ...(session && { session })
      });
      // 发送阻止邮件通知用户
      await sendBlockedMail(user.email, book.title, user.name);
      continue; // 跳过此预约，处理下一个
    }

    // ===== 5.6 准备借阅信息 =====
    // 计算借阅日期和归还日期。计算用户时区的归还日期（用于邮件显示）
    const issue_date = new Date(); // 当前时间作为借阅日期
    const { localEnd: datetoshow, utcEnd: return_date } = calEndOfDay(issue_date, MAX_BORROW_DAYS, user.offset);

    // ===== 5.7 减少库存（关键步骤）=====
    // 事務內部：使用原子操作減少庫存，不再調用 isBookInStock()
    // 先尝试减少库存，如果失败则停止处理后续预约
    // 这是防止超额借阅的关键检查点
    try {
      await BookHelper.updateAvailableStock(book_id, 'borrow', {
        session,
        preCheckedQuantity  // 傳遞事務前檢查的庫存數量
      });
    } catch (error) {
      console.log(`预约转借阅失败，库存不足: 用户 ${user._id}, 书籍 ${book_id}, 预约ID ${reserve._id}`);
      console.log('没有库存，停止处理');
      break; // 库存不足时停止处理后续预约，保证数据一致性
    }

    // ===== 5.8 创建借阅记录 =====
    // 库存减少成功后，为用户创建正式的借阅记录
    // 影响表：Borrow（借阅表）
    const mBorrow = new Borrow({
      email: user.email,
      user_id: user._id,
      book_id: mongoose.Types.ObjectId(book_id),
      issue_date,     // 借阅日期
      return_date,   // 应归还日期
      returned: false,            // 新借阅，未归还
      is_deleted: false           // 有效记录
    });
    await mBorrow.save({ ...(session && { session }) });

    // ===== 5.9 标记预约已处理 =====
    // 将预约标记为已处理，避免重复处理
    // 影响表：Reserve（预约表）
    await Reserve.findByIdAndUpdate(reserve._id, {
      is_mailed: true,        // 标记为已邮件通知（表示已处理）
      mail_date: new Date(),  // 记录处理时间
      is_deleted: true        // 标记为已删除（表示预约已完成）
    }, {
      useFindAndModify: false,
      ...(session && { session })
    });

    // ===== 5.10 发送邮件通知 =====
    // 通知用户预约已转为借阅，可以取书
    await sendBorrowMail(user.email, book.title, user.name, datetoshow);

    console.log(`预约转借阅成功: 用户 ${user._id}, 书籍 ${book_id}, 预约ID ${reserve._id}`);

  } // 结束 for 循环
}

module.exports = {
  syncReturnBooks
};
