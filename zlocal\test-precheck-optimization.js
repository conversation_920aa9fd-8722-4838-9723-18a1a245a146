const mongoose = require('mongoose');
const Book = require('../Model/Book');
const Borrow = require('../Model/Borrow');
const Reserve = require('../Model/Reserve');
const User = require('../Model/User');
const BookHelper = require('../Helper/BookHelper');
const { syncReturnBooks } = require('../src/action/bookActions');

// 加载环境变量
require('dotenv').config();

// 连接数据库
mongoose.connect(process.env.DB_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function testPrecheckOptimization() {
  console.log('=== 测试前置检查优化 ===');

  try {
    // 查找一本有库存的书
    const book = await Book.findOne({
      available_quantity: { $gte: 1 },
      stock_quantity: { $gte: 1 }
    });

    if (!book) {
      console.log('没有找到有库存的书籍');
      return;
    }

    // 设置测试环境：1本总库存，0本可用库存
    await Book.findByIdAndUpdate(book._id, {
      stock_quantity: 1,
      available_quantity: 0
    });

    console.log(`测试书籍: ${book.title}`);
    console.log('设置库存: 0/1 (无可用库存)');

    // 获取2个测试用户
    const testUsers = await User.find({ email: { $ne: '' } }).limit(2);
    if (testUsers.length < 2) {
      console.log('没有找到足够的测试用户');
      return;
    }

    console.log(`找到 ${testUsers.length} 个测试用户`);

    // 清理之前的测试数据
    await Reserve.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });
    await Borrow.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });

    // 创建2个预约
    const reservations = [];
    for (let i = 0; i < 2; i++) {
      const user = testUsers[i];
      const reserve = new Reserve({
        user_id: user._id,
        book_id: book._id,
        email: user.email,
        reserve_date: new Date(),
        status: true,
        is_mailed: false,
        is_deleted: false,
        is_blocked: false
      });
      await reserve.save();
      reservations.push(reserve);
      console.log(`用户${i + 1} (${user.email}) 创建预约`);
    }

    console.log('\n创建了 2 个预约，当前可用库存为 0');
    console.log('期望结果：两个用户都无法借阅（前置检查直接拒绝）');

    // 测试前置检查优化
    console.log('\n=== 测试前置检查优化 ===');

    // 直接测试 updateAvailableStock 函数
    console.log('\n1. 直接测试 updateAvailableStock 函数:');
    try {
      await BookHelper.updateAvailableStock(book._id.toString(), 'borrow');
      console.log('❌ 应该抛出错误但没有抛出');
    } catch (error) {
      console.log(`✅ 正确抛出错误: ${error.message}`);
    }

    // 测试 syncReturnBooks 函数
    console.log('\n2. 测试 syncReturnBooks 函数:');
    try {
      await syncReturnBooks(book._id.toString());
    } catch (error) {
      console.log('执行完成，可能有错误:', error.message);
    }

    // 检查结果
    const finalBook = await Book.findById(book._id);
    const borrowRecords = await Borrow.find({
      book_id: book._id,
      returned: false,
      user_id: { $in: testUsers.map(u => u._id) }
    });

    const processedReserves = await Reserve.find({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) },
      is_deleted: true
    });

    const remainingReserves = await Reserve.find({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) },
      is_deleted: false
    });

    console.log('\n=== 测试结果 ===');
    console.log(`最终库存: ${finalBook.available_quantity}/${finalBook.stock_quantity}`);
    console.log(`成功借阅用户数: ${borrowRecords.length}`);
    console.log(`已处理预约数: ${processedReserves.length}`);
    console.log(`剩余预约数: ${remainingReserves.length}`);

    // 验证前置检查的效果
    console.log('\n=== 验证前置检查优化效果 ===');

    if (finalBook.available_quantity === 0) {
      console.log('✅ 库存保持不变（前置检查生效）');
    } else {
      console.log('❌ 库存发生了意外变化');
    }

    if (borrowRecords.length === 0) {
      console.log('✅ 没有创建借阅记录（正确拒绝）');
    } else {
      console.log('❌ 意外创建了借阅记录');
    }

    if (remainingReserves.length === 2) {
      console.log('✅ 所有预约保持未处理状态');
    } else {
      console.log('❌ 预约状态发生了意外变化');
    }

    // 测试有库存的情况
    console.log('\n=== 测试有库存的情况 ===');

    // 增加库存
    await Book.findByIdAndUpdate(book._id, {
      available_quantity: 1
    });

    console.log('增加库存到 1/1');

    try {
      await BookHelper.updateAvailableStock(book._id.toString(), 'borrow');
      console.log('✅ 有库存时成功减少库存');
    } catch (error) {
      console.log(`❌ 有库存时意外失败: ${error.message}`);
    }

    // 检查库存是否正确减少
    const updatedBook = await Book.findById(book._id);
    if (updatedBook.available_quantity === 0) {
      console.log('✅ 库存正确减少到 0/1');
    } else {
      console.log(`❌ 库存减少异常: ${updatedBook.available_quantity}/1`);
    }

    // 清理测试数据
    await Borrow.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });
    await Reserve.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });

    // 恢复原始库存
    await Book.findByIdAndUpdate(book._id, {
      available_quantity: book.stock_quantity
    });

    console.log('\n测试数据已清理');

  } catch (error) {
    console.error('测试过程中出错:', error);
  } finally {
    await mongoose.connection.close();
  }
}

// 运行测试
testPrecheckOptimization();
