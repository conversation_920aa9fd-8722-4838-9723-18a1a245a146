module.exports = {
  env: {
    es6: true,
    node: true,
    browser: true,
    commonjs: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'script' // 改为script，因为使用CommonJS
  },
  rules: {

    // 变量相关
    'no-undef': 'error',
    'no-unused-vars': 'off', // 按用户要求允许未使用变量
    'no-undef-init': 'error',
    'prefer-const': 'error',
    'no-var': 'error',

    // 代码风格
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'never'],
    'no-trailing-spaces': 'error',
    'eol-last': 'error',

    // 空格和格式
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    'space-before-blocks': 'error',
    'keyword-spacing': 'error',

    // 最佳实践
    'eqeqeq': 0,
    'no-eval': 'error',
    'no-console': 'off', // 开发环境允许console
    'no-debugger': 'error',

    // 异步处理
    'no-async-promise-executor': 'error',
    'prefer-promise-reject-errors': 'error',

    // 代码复杂度
    'complexity': ['warn', 15],
    'max-depth': ['warn', 4],
    'max-lines-per-function': ['warn', 150],

    'no-constant-condition': 0,
  }
};
