const app = require('express').Router();
const mongoose = require('mongoose');
const multer = require('multer');

const Borrow = require('../Model/Borrow');
const Book = require('../Model/Book');
const User = require('../Model/User');
const Reserve = require('../Model/Reserve');

const BookHelper = require('../Helper/BookHelper');
const { syncReturnBooks } = require('../src/action/bookActions');

const upload = multer();

// ===== 缓存管理工具（兼容单机MongoDB版本） =====
const cache = {};

/**
 * 缓存管理工具类（无事务版本）
 * 专门用于单机 MongoDB 环境
 */
class CacheManager {
  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {string} operation - 操作类型
   * @param {number} expireMinutes - 过期时间（分钟）
   */
  static set(key, operation, expireMinutes = 10) {
    const expireTime = Date.now() + expireMinutes * 60 * 1000;
    cache[key] = {
      timestamp: Date.now(),
      expireTime: expireTime,
      operation: operation
    };

    // 设置自动清理定时器（兜底保障）
    setTimeout(() => {
      if (cache[key] && Date.now() >= cache[key].expireTime) {
        delete cache[key];
        console.log(`Cache auto-expired: ${key}`);
      }
    }, expireMinutes * 60 * 1000);

    console.log(`Cache set: ${key} (expires in ${expireMinutes} minutes)`);
  }

  /**
   * 检查缓存是否存在
   * @param {string} key - 缓存键
   * @returns {boolean} 是否存在
   */
  static exists(key) {
    if (!cache[key]) return false;
    
    // 检查是否过期
    if (Date.now() >= cache[key].expireTime) {
      delete cache[key];
      console.log(`Cache expired and removed: ${key}`);
      return false;
    }
    
    return true;
  }

  /**
   * 清除缓存
   * @param {string} key - 缓存键
   * @param {string} reason - 清除原因
   */
  static clear(key, reason = 'normal') {
    if (cache[key]) {
      delete cache[key];
      console.log(`Cache cleared (${reason}): ${key}`);
    }
  }

  /**
   * 清理所有过期缓存
   */
  static cleanExpired() {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const key in cache) {
      if (cache[key].expireTime <= now) {
        delete cache[key];
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`Cleaned ${cleanedCount} expired cache entries`);
    }
  }
}

// 定期清理过期缓存（每5分钟执行一次）
setInterval(() => {
  CacheManager.cleanExpired();
}, 5 * 60 * 1000);

app.use('/*', (req, res, next) => next());

/**
 * 借书接口（无事务版本，兼容单机MongoDB）
 * 使用原子操作和补偿机制确保数据一致性
 */
app.post('/borrow', upload.none(), async (req, res) => {
  console.log('Borrow API called (No Transaction):', req.body);

  // ===== 参数验证 =====
  if (!req.body.user_id || !req.body.book_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }

  const user_id = req.body.user_id;
  const book_id = req.body.book_id;

  // ===== 防重复提交机制 =====
  const cacheKey = 'borrow_' + user_id + '_' + book_id;
  
  // 检查是否正在处理中
  if (CacheManager.exists(cacheKey)) {
    return res.json({
      code: 418,
      message: 'Processing in progress',
      status: false
    });
  }
  
  // 设置缓存标记，10分钟过期
  CacheManager.set(cacheKey, 'borrow', 10);

  try {
    // ===== 第一步：验证用户邮箱 =====
    const user = await User.findOne({ _id: user_id });
    if (!user || !user.email) {
      CacheManager.clear(cacheKey, 'invalid email');
      return res.json({
        code: 422,
        message: 'User email not found',
        status: false
      });
    }

    // ===== 第二步：检查是否已经借阅 =====
    const existingBorrow = await Borrow.findOne({
      user_id: user_id,
      book_id: book_id,
      returned: false
    });

    if (existingBorrow) {
      CacheManager.clear(cacheKey, 'already borrowed');
      return res.json({
        code: 200,
        message: 'Book already borrowed',
        status: true
      });
    }

    // ===== 第三步：原子操作检查并减少库存 =====
    // 使用 findOneAndUpdate 的原子操作特性
    const bookUpdateResult = await Book.findOneAndUpdate(
      {
        _id: mongoose.Types.ObjectId(book_id),
        available_quantity: { $gt: 0 }  // 确保库存大于0
      },
      {
        $inc: { available_quantity: -1 }  // 原子减1
      },
      {
        new: true,  // 返回更新后的文档
        runValidators: true
      }
    );

    if (!bookUpdateResult) {
      CacheManager.clear(cacheKey, 'no stock');
      return res.json({
        code: 401,
        message: 'Book not available',
        status: false
      });
    }

    console.log(`Stock decreased for book ${book_id}: ${bookUpdateResult.available_quantity + 1} -> ${bookUpdateResult.available_quantity}`);

    // ===== 第四步：创建借阅记录 =====
    let borrowRecord;
    try {
      const newBorrow = new Borrow({
        user_id: user_id,
        book_id: book_id,
        borrow_date: new Date(),
        due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
        returned: false
      });
      
      borrowRecord = await newBorrow.save();
      console.log(`Borrow record created: ${borrowRecord._id}`);

    } catch (error) {
      console.error('Failed to create borrow record:', error);
      
      // 补偿操作：恢复库存
      await Book.findOneAndUpdate(
        { _id: mongoose.Types.ObjectId(book_id) },
        { $inc: { available_quantity: 1 } }
      );
      console.log('Stock restored due to borrow record creation failure');
      
      CacheManager.clear(cacheKey, 'borrow record creation failed');
      return res.json({
        code: 500,
        message: 'Failed to create borrow record',
        status: false
      });
    }

    // ===== 第五步：处理等待中的预约（如果有） =====
    try {
      // 注意：syncReturnBooks 也需要修改为无事务版本
      await syncReturnBooks(book_id);
    } catch (error) {
      console.error('Failed to process reservations:', error);
      // 预约处理失败不影响借书成功，只记录日志
    }

    // ===== 成功，清除缓存并返回结果 =====
    CacheManager.clear(cacheKey, 'borrow success');
    
    res.json({
      code: 200,
      message: 'Book borrowed successfully',
      data: {
        borrow_id: borrowRecord._id
      },
      status: true
    });

    // ===== 异步发送邮件（不影响响应） =====
    // 这里可以添加邮件发送逻辑

  } catch (error) {
    console.error('Borrow operation error:', error);
    CacheManager.clear(cacheKey, 'operation error');
    
    res.json({
      code: 500,
      message: 'Failed to borrow book',
      status: false
    });
  }
});

module.exports = app;
