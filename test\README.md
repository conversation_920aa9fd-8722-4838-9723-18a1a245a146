# 预约处理测试用例

## 🎯 测试目标

验证修复后的预约处理逻辑，确保不再出现"跳跃式"处理现象。

### 问题背景
- **修复前**：3个阅读人归还，4个预约等待，出现跳跃式处理
  - 第1次归还：没有触发预约
  - 第2次归还：触发用户1和用户2的预约
  - 第3次归还：没有触发预约
  - 第4次归还：触发用户3和用户4的预约

- **修复后**：应该按顺序处理预约
  - 第1次归还：触发用户1的预约
  - 第2次归还：触发用户2的预约
  - 第3次归还：触发用户3的预约

## 🔧 测试环境

### 数据库配置
- **连接**: `***********************************************************`
- **环境文件**: `zlocal/.env`

### 测试数据要求
- 至少1本有库存的书籍
- 至少7个活跃用户（3个用于阅读，4个用于预约）

## 🚀 运行测试

### 1. 快速检查数据库状态
```bash
npm run test:check
```
或
```bash
node test/run-test.js --check
```

**输出示例**：
```
🔍 快速检查数据库状态...
📚 可用书籍数: 15
📖 示例书籍:
  1. JavaScript高级程序设计 - 库存: 3/5 (ID: 507f1f77bcf86cd799439011)
  2. Node.js实战 - 库存: 2/3 (ID: 507f1f77bcf86cd799439012)

👥 活跃用户数: 25
👤 示例用户:
  1. 张三 (ID: 507f1f77bcf86cd799439021)
  2. 李四 (ID: 507f1f77bcf86cd799439022)
```

### 2. 创建测试预约
```bash
npm run test:create-reserves
```

这会：
- 选择一本有库存的书籍
- 为4个用户创建预约记录
- 清理旧的测试数据

### 3. 运行完整测试
```bash
npm run test:reservation
```

这会：
1. 检查数据库状态
2. 创建测试预约
3. 模拟3次书籍归还
4. 观察预约处理结果
5. 输出测试总结

## 📊 测试结果解读

### 成功的测试结果
```
📊 测试结果总结:
========================================
第1次归还:
  - 库存变化: 0 -> 1
  - 处理预约: 1
  - 新借阅: 1

第2次归还:
  - 库存变化: 0 -> 1
  - 处理预约: 1
  - 新借阅: 1

第3次归还:
  - 库存变化: 0 -> 1
  - 处理预约: 1
  - 新借阅: 1

总计:
  - 总处理预约: 3
  - 总新借阅: 3

✅ 测试通过！预约处理正常
```

### 异常的测试结果
如果出现以下情况，说明还有问题：
- 某次归还处理了0个预约
- 某次归还处理了多个预约
- 库存变化不正确
- 新借阅记录数量不匹配

## 🔍 调试信息

### 查看详细日志
测试过程中会输出详细的日志信息：
- 库存变化记录
- 预约处理状态
- 新借阅记录创建
- 错误信息（如果有）

### 手动验证
可以直接连接数据库查看：

```javascript
// 查看特定书籍的预约
db.reserves.find({
  book_id: ObjectId("书籍ID"),
  is_deleted: false
}).sort({reserve_date: 1})

// 查看特定书籍的借阅记录
db.borrows.find({
  book_id: ObjectId("书籍ID"),
  returned: false
}).sort({issue_date: -1})

// 查看书籍库存
db.books.findOne({_id: ObjectId("书籍ID")})
```

## 🛠️ 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `zlocal/.env` 文件中的 `DB_URL`
   - 确认数据库服务器可访问

2. **没有足够的测试数据**
   - 确保数据库中有足够的用户和书籍
   - 可以手动创建测试数据

3. **测试结果异常**
   - 检查控制台日志中的错误信息
   - 验证 `isBookInStock()` 和 `updateAvailableStock()` 函数的修改
   - 确认 `syncReturnBooks()` 函数的逻辑

### 清理测试数据
测试会自动清理创建的测试数据，但如果需要手动清理：

```javascript
// 删除测试预约
db.reserves.deleteMany({
  email: /test\d+@example\.com/
})

// 删除测试借阅记录
db.borrows.deleteMany({
  email: /test\d+@example\.com/
})
```

## 📝 测试文件说明

- `test/run-test.js` - 主要测试脚本
- `test/reservation-processing-test.js` - 详细的测试用例（备用）
- `test/README.md` - 本说明文档

## 🎯 核心验证点

1. **事务前预检查**：确认 `isBookInStock()` 只在事务外调用
2. **原子操作**：确认事务内只使用 `findOneAndUpdate` 等原子操作
3. **顺序处理**：确认预约按时间顺序处理，不跳跃
4. **库存一致性**：确认库存变化与预约处理数量匹配
5. **错误处理**：确认库存不足时正确停止处理

## 📞 支持

如果测试过程中遇到问题，请检查：
1. 控制台输出的详细日志
2. 数据库中的实际数据状态
3. 修改后的代码逻辑是否正确实现

测试成功后，说明预约处理"跳跃式"问题已经解决！🎉
