/**
 * 测试运行脚本
 * 
 * 使用方法：
 * node test/run-test.js                    # 运行完整测试
 * node test/run-test.js --check           # 快速检查数据库状态
 * node test/run-test.js --setup-only      # 只设置测试环境
 * node test/run-test.js --cleanup-only    # 只清理测试数据
 */

require('dotenv').config({ path: './zlocal/.env' });

const mongoose = require('mongoose');
const User = require('../Model/User');
const Book = require('../Model/Book');
const Reserve = require('../Model/Reserve');
const Reading = require('../Model/Reading');
const Borrow = require('../Model/Borrow');
const BookHelper = require('../Helper/BookHelper');

// 从环境变量获取数据库连接
const DB_URL = process.env.DB_URL || 'mongodb://122.248.233.120:27017/ebook';

console.log('🔗 数据库连接:', DB_URL);

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useCreateIndex: true,
      useFindAndModify: false
    });
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
}

/**
 * 快速检查数据库状态
 */
async function quickCheck() {
  console.log('\n🔍 快速检查数据库状态...');
  
  try {
    // 检查书籍数量和库存
    const books = await Book.find({ 
      is_deleted: false,
      available_quantity: { $gte: 1 }
    }).limit(5);
    
    console.log(`📚 可用书籍数: ${books.length}`);
    if (books.length > 0) {
      console.log('📖 示例书籍:');
      books.forEach((book, index) => {
        console.log(`  ${index + 1}. ${book.title} - 库存: ${book.available_quantity}/${book.stock_quantity} (ID: ${book._id})`);
      });
    }
    
    // 检查用户数量
    const users = await User.find({ is_active: true }).limit(10);
    console.log(`\n👥 活跃用户数: ${users.length}`);
    if (users.length > 0) {
      console.log('👤 示例用户:');
      users.slice(0, 5).forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.name || user.patronid || 'Unknown'} (ID: ${user._id})`);
      });
    }
    
    // 检查预约数量
    const reserves = await Reserve.find({ is_deleted: false }).populate('user_id').populate('book_id').limit(5);
    console.log(`\n📋 活跃预约数: ${reserves.length}`);
    if (reserves.length > 0) {
      console.log('📝 示例预约:');
      reserves.forEach((reserve, index) => {
        const userName = reserve.user_id?.name || reserve.user_id?.patronid || 'Unknown';
        const bookTitle = reserve.book_id?.title || 'Unknown';
        console.log(`  ${index + 1}. ${userName} -> ${bookTitle}`);
      });
    }
    
    // 检查借阅数量
    const borrows = await Borrow.find({ returned: false }).populate('user_id').populate('book_id').limit(5);
    console.log(`\n📖 活跃借阅数: ${borrows.length}`);
    
    // 检查阅读数量
    const readings = await Reading.find({ returned: false }).populate('book_id').limit(5);
    console.log(`📱 活跃阅读数: ${readings.length}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    return false;
  }
}

/**
 * 创建测试预约
 */
async function createTestReservations() {
  console.log('\n📋 创建测试预约...');
  
  try {
    // 获取一本有库存的书
    const book = await Book.findOne({
      is_deleted: false,
      available_quantity: { $gte: 1 }
    });
    
    if (!book) {
      console.log('❌ 没有找到可用的书籍');
      return false;
    }
    
    // 获取4个用户
    const users = await User.find({ is_active: true }).limit(4);
    if (users.length < 4) {
      console.log(`❌ 需要至少4个用户，当前只有${users.length}个`);
      return false;
    }
    
    console.log(`📚 选择书籍: ${book.title} (ID: ${book._id})`);
    console.log(`📦 当前库存: ${book.available_quantity}/${book.stock_quantity}`);
    
    // 清理该书的旧预约
    await Reserve.deleteMany({
      book_id: book._id,
      user_id: { $in: users.map(u => u._id) }
    });
    
    // 创建4个预约
    const reservePromises = users.map((user, index) => {
      return new Reserve({
        user_id: user._id,
        book_id: book._id,
        email: user.email || `test${index}@example.com`,
        reserve_date: new Date(Date.now() + index * 1000), // 确保时间不同
        status: true,
        is_deleted: false,
        is_mailed: false
      }).save();
    });
    
    const savedReserves = await Promise.all(reservePromises);
    
    console.log('✅ 成功创建预约:');
    savedReserves.forEach((reserve, index) => {
      const user = users[index];
      console.log(`  ${index + 1}. ${user.name || user.patronid} (${user._id})`);
    });
    
    return {
      book_id: book._id.toString(),
      book_title: book.title,
      users: users,
      reserves: savedReserves
    };
    
  } catch (error) {
    console.error('❌ 创建预约失败:', error.message);
    return false;
  }
}

/**
 * 模拟书籍归还并触发预约处理
 */
async function simulateReturnAndReserve(bookId) {
  console.log(`\n📖 模拟书籍归还并处理预约 (书籍ID: ${bookId})...`);
  
  try {
    // 导入 syncReturnBooks 函数
    const { syncReturnBooks } = require('../src/action/bookActions');
    
    // 检查归还前状态
    const bookBefore = await Book.findById(bookId);
    const reservesBefore = await Reserve.find({
      book_id: mongoose.Types.ObjectId(bookId),
      is_deleted: false
    }).populate('user_id');
    
    console.log(`📦 归还前库存: ${bookBefore.available_quantity}`);
    console.log(`📋 等待中的预约: ${reservesBefore.length}`);
    
    if (reservesBefore.length > 0) {
      console.log('👥 等待预约的用户:');
      reservesBefore.forEach((reserve, index) => {
        const userName = reserve.user_id?.name || reserve.user_id?.patronid || 'Unknown';
        console.log(`  ${index + 1}. ${userName} (预约时间: ${reserve.reserve_date})`);
      });
    }
    
    // 模拟归还：增加库存
    await BookHelper.updateAvailableStock(bookId, 'return');
    
    // 处理预约
    console.log('\n🔄 开始处理预约...');
    await syncReturnBooks(bookId);
    
    // 检查处理后状态
    const bookAfter = await Book.findById(bookId);
    const reservesAfter = await Reserve.find({
      book_id: mongoose.Types.ObjectId(bookId),
      is_deleted: false
    }).populate('user_id');
    
    const newBorrows = await Borrow.find({
      book_id: mongoose.Types.ObjectId(bookId),
      returned: false
    }).populate('user_id').sort({ issue_date: -1 }).limit(5);
    
    console.log(`\n📊 处理结果:`);
    console.log(`📦 库存变化: ${bookBefore.available_quantity} -> ${bookAfter.available_quantity}`);
    console.log(`📋 剩余预约: ${reservesAfter.length}`);
    console.log(`📚 新借阅记录: ${newBorrows.length}`);
    
    if (newBorrows.length > 0) {
      console.log('👤 新借阅用户:');
      newBorrows.forEach((borrow, index) => {
        const userName = borrow.user_id?.name || borrow.user_id?.patronid || 'Unknown';
        console.log(`  ${index + 1}. ${userName} (借阅时间: ${borrow.issue_date})`);
      });
    }
    
    return {
      stockBefore: bookBefore.available_quantity,
      stockAfter: bookAfter.available_quantity,
      reservesBefore: reservesBefore.length,
      reservesAfter: reservesAfter.length,
      newBorrows: newBorrows.length
    };
    
  } catch (error) {
    console.error('❌ 模拟归还失败:', error.message);
    return false;
  }
}

/**
 * 运行完整测试流程
 */
async function runFullTest() {
  console.log('🚀 开始预约处理测试...');
  console.log('=' * 60);
  
  try {
    // 1. 创建测试预约
    const testData = await createTestReservations();
    if (!testData) {
      console.log('❌ 测试数据创建失败');
      return;
    }
    
    console.log('\n⏳ 等待3秒，确保数据保存完成...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 2. 模拟3次归还，观察预约处理
    console.log('\n🎯 开始模拟归还操作...');
    const results = [];
    
    for (let i = 1; i <= 3; i++) {
      console.log(`\n--- 第${i}次归还 ---`);
      const result = await simulateReturnAndReserve(testData.book_id);
      if (result) {
        results.push(result);
      }
      
      // 等待2秒
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // 3. 总结测试结果
    console.log('\n📊 测试结果总结:');
    console.log('=' * 40);
    
    let totalProcessed = 0;
    let totalBorrows = 0;
    
    results.forEach((result, index) => {
      const processed = result.reservesBefore - result.reservesAfter;
      console.log(`第${index + 1}次归还:`);
      console.log(`  - 库存变化: ${result.stockBefore} -> ${result.stockAfter}`);
      console.log(`  - 处理预约: ${processed}`);
      console.log(`  - 新借阅: ${result.newBorrows}`);
      
      totalProcessed += processed;
      totalBorrows += result.newBorrows;
    });
    
    console.log(`\n总计:`);
    console.log(`  - 总处理预约: ${totalProcessed}`);
    console.log(`  - 总新借阅: ${totalBorrows}`);
    
    // 验证结果
    if (totalProcessed >= 3 && totalBorrows >= 3) {
      console.log('\n✅ 测试通过！预约处理正常');
    } else {
      console.log('\n⚠️  测试结果异常，请检查日志');
    }
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  // 连接数据库
  const connected = await connectDB();
  if (!connected) {
    process.exit(1);
  }
  
  try {
    if (args.includes('--check')) {
      await quickCheck();
    } else if (args.includes('--create-reserves')) {
      await createTestReservations();
    } else {
      await quickCheck();
      await runFullTest();
    }
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ 数据库连接已断开');
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  connectDB,
  quickCheck,
  createTestReservations,
  simulateReturnAndReserve,
  runFullTest
};
