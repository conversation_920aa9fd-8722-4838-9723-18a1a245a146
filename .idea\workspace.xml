<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a64dc570-1ad1-4b1b-bee6-491b88153052" name="Changes" comment="fix: transaction" />
    <list id="3e83691a-3c8f-4a33-bdd1-2232dfc9acd0" name="cc" comment="">
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PREVIEW_PUSH_PROTECTED_ONLY" value="true" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="uat" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="largeFiles">
      <LargeFiles>
        <option name="unlockedFilePaths">
          <set>
            <option value="$PROJECT_DIR$/Routes/Book.js" />
          </set>
        </option>
      </LargeFiles>
    </option>
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="uat-mongo" />
                    <option name="lastUsedInstant" value="1753670404" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="uat" />
                    <option name="lastUsedInstant" value="1753669587" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="30Am1pHRVzu4wmhT9a95F7ukVJo" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Node.js.bookActions.js.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;uat-mongo&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/HXL_Book/jrc-service/zlocal&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;yarn&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Program Files\\JetBrains\\WebStorm 2025.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\HXL_Book\jrc-service\zlocal" />
      <recent name="E:\HXL_Book\jrc-service" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\HXL_Book\jrc-service\zlocal" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="bookActions.js" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="$PROJECT_DIR$/src/action/bookActions.js" working-dir="$PROJECT_DIR$/src/action">
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Node.js.bookActions.js" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a64dc570-1ad1-4b1b-bee6-491b88153052" name="Changes" comment="fix: sendBorrowMail" />
      <created>1753078540667</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753078540667</updated>
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="uat" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="fix: bookHelper, valid stock, update stock" />
    <MESSAGE value="fix: spell" />
    <MESSAGE value="fix: stock update sync" />
    <MESSAGE value="fix: continue next reading, $inc" />
    <MESSAGE value="fix: return_date" />
    <MESSAGE value="optimize: book get" />
    <MESSAGE value="optimize: book $addFields" />
    <MESSAGE value="optimize: bookQtyUpdate not only from Borrow. 2. update book stock total" />
    <MESSAGE value="optimize: update avail stock, count returned false occupy" />
    <MESSAGE value="optimize: docker cache" />
    <MESSAGE value="optimize: aggregate total borrow reserve" />
    <MESSAGE value="optimize: syncReturnBooks" />
    <MESSAGE value="optimize: reserve failed success mail" />
    <MESSAGE value="optimize: node-cron clean" />
    <MESSAGE value="optimize: updateAvailableStock" />
    <MESSAGE value="optimize: route book, borrow, reading" />
    <MESSAGE value="lint: book" />
    <MESSAGE value="lint: borrow" />
    <MESSAGE value="fix: acid" />
    <MESSAGE value="fix: stock lint" />
    <MESSAGE value="fix: improve process consistency" />
    <MESSAGE value="fix: transaction" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: transaction" />
  </component>
</project>