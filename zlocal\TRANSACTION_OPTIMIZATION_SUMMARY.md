# 事务优化总结：解决预约处理"跳跃式"问题

## 🎯 问题根源

用户发现预约处理出现"跳跃式"现象：
- 第1次归还：没有触发任何预约
- 第2次归还：触发了用户1和用户2的预约  
- 第3次归还：没有触发任何预约
- 第4次归还：触发了用户3和用户4的预约

**根本原因**：在事务内部调用 `isBookInStock()` 函数导致库存计算不准确。

## 🔍 技术分析

### 问题详情：
1. **`isBookInStock()` 在事务内部调用的问题**：
   - 事务内部状态正在变化（Borrow/Reading 记录正在被创建/删除）
   - `isBookInStock()` 通过查询 Borrow/Reading 记录来计算可用库存
   - 在事务中，这些记录可能处于不一致状态
   - 导致计算出的库存数量与实际数据库字段不匹配

2. **时机问题**：
   - `isBookInStock()` 适合在**事务外部**用于修正数据
   - 不适合在**事务内部**用于实时决策

3. **日志证据**：
   ```
   Stock decreased for borrow: 1 -> 1  ❌ 不可能的结果
   Stock decreased for borrow: 1 -> 0  ✅ 正常结果
   ```

## ✅ 解决方案

### 设计原则：
1. **事务开始前**：使用 `isBookInStock()` 做预检查
2. **事务内部**：只使用原子操作，不再调用 `isBookInStock()`
3. **所有相关模块**：Routes、BookHelper、src 都统一这个原则

### 修改内容：

#### 1. Helper/BookHelper.js
```javascript
/**
 * 更新图书可用库存
 * @param {string} bookId - 图书ID
 * @param {string} type - 操作类型: 'borrow'|'reading'|'return'
 * @param {Object} [options] - 选项
 * @param {Object} [options.session] - Mongoose 会话（用于事务）
 * @param {number} [options.preCheckedQuantity] - 事务前预检查的库存数量（用于日志显示）
 */
async function updateAvailableStock(bookId, type, { session, preCheckedQuantity } = {}) {
  if (['reading', 'borrow'].includes(type)) {
    // ===== 事务内部：只使用原子操作，不再调用 isBookInStock() =====
    // 原因：事务内部状态正在变化，isBookInStock() 计算结果不可靠
    
    const result = await Book.findOneAndUpdate(
      {
        _id: mongoose.Types.ObjectId(bookId),
        available_quantity: { $gt: 0 }  // 原子检查：确保库存大于0
      },
      { $inc: { available_quantity: -1 } },
      { new: true, session }
    );
    
    if (result) {
      // 使用预检查的数量来显示日志（如果提供的话）
      const beforeQuantity = preCheckedQuantity !== undefined ? preCheckedQuantity : result.available_quantity + 1;
      console.log(`Stock decreased for ${type}: ${beforeQuantity} -> ${result.available_quantity}`);
    } else {
      // 原子操作失败，说明库存不足
      console.log(`Stock decrease failed for ${type}: 当前库存为0，无法减少`);
      throw new Error(`Stock decrease failed for ${type}: 当前库存为0，无法减少库存`);
    }
    return;
  }
  // ... return 逻辑保持不变
}
```

#### 2. Routes/Borrow.js
```javascript
// ===== 事務前預檢查：使用 isBookInStock() 做初步庫存檢查 =====
const preCheckedQuantity = await BookHelper.isBookInStock(book_id);
if (preCheckedQuantity <= 0) {
  res.json({
    code: 422,
    message: 'Book is out of stock!',
    status: false
  });
  CacheManager.clear(cacheKey, 'book out of stock');
  return res.end();
}

// 使用 MongoDB 事務確保原子性
const session = await mongoose.startSession();

try {
  await session.withTransaction(async () => {
    // ===== 事務內部：只使用原子操作，不再調用 isBookInStock() =====
    const bookUpdateResult = await Book.findOneAndUpdate(
      {
        _id: mongoose.Types.ObjectId(book_id),
        available_quantity: { $gt: 0 }
      },
      { $inc: { available_quantity: -1 } },
      { new: true, session }
    );

    if (!bookUpdateResult) {
      throw new Error('STOCK_OUT');
    }

    // 記錄庫存變化日誌
    console.log(`Stock decreased for borrow: ${preCheckedQuantity} -> ${bookUpdateResult.available_quantity}`);
    // ... 其他业务逻辑
  });
}
```

#### 3. Routes/Reading.js
```javascript
// ===== 事務前預檢查：使用 isBookInStock() 做初步庫存檢查 =====
const preCheckedQuantity = await BookHelper.isBookInStock(book_id);
if (preCheckedQuantity <= 0) {
  ReadingCacheManager.clear(cacheKey, 'book out of stock');
  return res.json({
    code: 401,
    message: 'Book not has stock',
    status: false
  });
}

// 创建数据库会话用于事务
const session = await mongoose.startSession();

try {
  await session.withTransaction(async () => {
    // ===== 事務內部：只使用原子操作，不再調用 isBookInStock() =====
    await BookHelper.updateAvailableStock(book_id, 'reading', { 
      session, 
      preCheckedQuantity 
    });
    // ... 其他业务逻辑
  });
}
```

#### 4. src/action/bookActions.js
```javascript
async function syncReturnBooks(book_id, { session } = {}) {
  // ===== 事務前預檢查：使用 isBookInStock() 確認是否有庫存可處理預約 =====
  const preCheckedQuantity = await BookHelper.isBookInStock(book_id);
  if (preCheckedQuantity <= 0) {
    console.log('事務前檢查：沒有庫存，跳過預約處理');
    return;
  }

  // ... 其他逻辑

  for (const reserve of reservations) {
    // ===== 事務內部：使用原子操作減少庫存，不再調用 isBookInStock() =====
    try {
      await BookHelper.updateAvailableStock(book_id, 'borrow', { 
        session,
        preCheckedQuantity  // 傳遞事務前檢查的庫存數量
      });
    } catch (error) {
      console.log(`预约转借阅失败，库存不足: 用户 ${user._id}, 书籍 ${book_id}, 预约ID ${reserve._id}`);
      console.log('没有库存，停止处理');
      break; // 库存不足时停止处理后续预约，保证数据一致性
    }
    // ... 其他业务逻辑
  }
}
```

## 🎯 预期效果

修改后，预约处理应该变为顺序处理：
- 第1次归还：正确触发第1个用户的预约
- 第2次归还：正确触发第2个用户的预约  
- 第3次归还：正确触发第3个用户的预约
- 第4次归还：正确触发第4个用户的预约

## 📝 关键要点

1. **`isBookInStock()` 的正确用途**：
   - ✅ 事务外部的数据修正
   - ✅ 定期的库存校验
   - ✅ 前端显示用的库存查询
   - ✅ 非事务性的库存检查
   - ❌ 事务内部的实时决策

2. **事务内部的最佳实践**：
   - ✅ 使用原子操作和数据库约束
   - ✅ 依赖 `findOneAndUpdate` 的条件检查
   - ✅ 使用 `available_quantity: { $gt: 0 }` 确保库存充足
   - ❌ 调用复杂的计算函数

3. **数据一致性保证**：
   - 事务前预检查避免不必要的事务开销
   - 事务内原子操作确保数据一致性
   - 错误处理确保事务回滚时的数据完整性

## 🔧 测试建议

1. **功能测试**：重新测试3个阅读人归还、4个预约的场景
2. **并发测试**：多个用户同时操作同一本书
3. **边界测试**：库存为0时的各种操作
4. **日志验证**：确认库存变化日志的准确性

这个优化解决了事务内部状态不一致导致的预约处理问题，确保了库存管理的准确性和预约处理的公平性。
