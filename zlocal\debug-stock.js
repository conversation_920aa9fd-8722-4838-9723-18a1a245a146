/**
 * 庫存診斷腳本
 * 用於調試和驗證書籍庫存的一致性
 */

require('dotenv').config();
const mongoose = require('mongoose');
const Book = require('../Model/Book');
const Borrow = require('../Model/Borrow');
const Reading = require('../Model/Reading');
const Reserve = require('../Model/Reserve');
const BookHelper = require('../Helper/BookHelper');

/**
 * 檢查單本書籍的庫存一致性
 */
async function checkBookStock(bookId) {
  try {
    console.log(`\n=== 檢查書籍 ${bookId} 的庫存 ===`);

    // 1. 獲取書籍基本信息
    const book = await Book.findOne({ _id: mongoose.Types.ObjectId(bookId) });
    if (!book) {
      console.log('書籍不存在');
      return;
    }

    console.log(`書籍: ${book.title}`);
    console.log(`總庫存: ${book.stock_quantity}`);
    console.log(`可用庫存: ${book.available_quantity}`);

    // 2. 統計實際借閱數量
    const borrowCount = await Borrow.countDocuments({
      book_id: mongoose.Types.ObjectId(bookId),
      returned: false,
      is_deleted: false
    });

    // 3. 統計實際閱讀數量
    const readingCount = await Reading.countDocuments({
      book_id: mongoose.Types.ObjectId(bookId),
      returned: false
    });

    // 4. 統計預約數量
    const reserveCount = await Reserve.countDocuments({
      book_id: mongoose.Types.ObjectId(bookId),
      is_deleted: false
    });

    console.log(`實際借閱數量: ${borrowCount}`);
    console.log(`實際閱讀數量: ${readingCount}`);
    console.log(`預約數量: ${reserveCount}`);

    // 5. 計算理論可用庫存
    const theoreticalAvailable = book.stock_quantity - borrowCount - readingCount;
    console.log(`理論可用庫存: ${theoreticalAvailable}`);

    // 6. 檢查一致性
    const isConsistent = book.available_quantity === theoreticalAvailable;
    console.log(`庫存一致性: ${isConsistent ? '✓ 一致' : '✗ 不一致'}`);

    if (!isConsistent) {
      console.log(`差異: ${book.available_quantity - theoreticalAvailable}`);
    }

    // 7. 使用 BookHelper 重新計算
    const helperStock = await BookHelper.isBookInStock(bookId);
    console.log(`BookHelper 計算結果: ${helperStock}`);

    return {
      bookId,
      title: book.title,
      stockQuantity: book.stock_quantity,
      availableQuantity: book.available_quantity,
      borrowCount,
      readingCount,
      reserveCount,
      theoreticalAvailable,
      helperStock,
      isConsistent
    };

  } catch (error) {
    console.error(`檢查書籍 ${bookId} 時發生錯誤:`, error);
    return null;
  }
}

/**
 * 修復書籍庫存
 */
async function fixBookStock(bookId) {
  try {
    console.log(`\n=== 修復書籍 ${bookId} 的庫存 ===`);

    const book = await Book.findOne({ _id: mongoose.Types.ObjectId(bookId) });
    if (!book) {
      console.log('書籍不存在');
      return;
    }

    // 重新計算正確的庫存
    const borrowCount = await Borrow.countDocuments({
      book_id: mongoose.Types.ObjectId(bookId),
      returned: false,
      is_deleted: false
    });

    const readingCount = await Reading.countDocuments({
      book_id: mongoose.Types.ObjectId(bookId),
      returned: false
    });

    const correctAvailable = Math.max(0, book.stock_quantity - borrowCount - readingCount);

    console.log(`修復前可用庫存: ${book.available_quantity}`);
    console.log(`修復後可用庫存: ${correctAvailable}`);

    // 更新數據庫
    await Book.findOneAndUpdate(
      { _id: mongoose.Types.ObjectId(bookId) },
      { available_quantity: correctAvailable }
    );

    console.log('庫存修復完成');

    return correctAvailable;

  } catch (error) {
    console.error(`修復書籍 ${bookId} 庫存時發生錯誤:`, error);
    return null;
  }
}

/**
 * 檢查所有書籍的庫存一致性
 */
async function checkAllBooksStock() {
  try {
    console.log('\n=== 檢查所有書籍的庫存一致性 ===');

    const books = await Book.find({
      is_deleted: { $ne: true },
      stock_quantity: { $gt: 0 }
    }).limit(10);

    console.log(`找到 ${books.length} 本書籍`);

    const results = [];
    for (const book of books) {
      const result = await checkBookStock(book._id);
      if (result) {
        results.push(result);
      }
    }

    // 統計結果
    const inconsistentBooks = results.filter(r => !r.isConsistent);
    console.log('\n=== 統計結果 ===');
    console.log(`總檢查書籍: ${results.length}`);
    console.log(`庫存一致: ${results.length - inconsistentBooks.length}`);
    console.log(`庫存不一致: ${inconsistentBooks.length}`);

    if (inconsistentBooks.length > 0) {
      console.log('\n不一致的書籍:');
      inconsistentBooks.forEach(book => {
        console.log(`- ${book.title} (ID: ${book.bookId})`);
        console.log(`  數據庫: ${book.availableQuantity}, 理論: ${book.theoreticalAvailable}`);
      });
    }

    return results;

  } catch (error) {
    console.error('檢查所有書籍庫存時發生錯誤:', error);
    return [];
  }
}

/**
 * 主函數
 */
async function main() {
  try {
    // 連接數據庫
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/jrc-service');
    console.log('數據庫連接成功');

    const args = process.argv.slice(2);
    const command = args[0];
    const bookId = args[1];

    switch (command) {
    case 'check':
      if (bookId) {
        await checkBookStock(bookId);
      } else {
        await checkAllBooksStock();
      }
      break;

    case 'fix':
      if (bookId) {
        await fixBookStock(bookId);
      } else {
        console.log('請提供書籍ID: node debug-stock.js fix <bookId>');
      }
      break;

    default:
      console.log('使用方法:');
      console.log('  node debug-stock.js check [bookId]  - 檢查庫存一致性');
      console.log('  node debug-stock.js fix <bookId>    - 修復指定書籍庫存');
      break;
    }

  } catch (error) {
    console.error('執行過程中發生錯誤:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n數據庫連接已關閉');
  }
}

// 如果直接運行此腳本
if (require.main === module) {
  main();
}

module.exports = {
  checkBookStock,
  fixBookStock,
  checkAllBooksStock
};
