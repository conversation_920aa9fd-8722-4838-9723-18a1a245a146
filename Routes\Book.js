const app = require('express').Router();
const mongoose = require('mongoose');
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const {
  PDFNet
} = require('@pdftron/pdfnet-node'); //you may need to set up NODE_PATH environment variable to make this work.
const moment = require('moment-timezone');
const _ = require('lodash');

const Admin = require('../Model/Admin');
const { AdminHelper } = require('../Helper/AdminHelper');
const { getBooks, filterRecords } = require('../src/service/book/bookGet');
const Book = require('../Model/Book');
const Banner = require('../Model/Banner');
const Category = require('../Model/Category');
const AdminSession = require('../Model/AdminSession');
const UserSession = require('../Model/UserSession');
const Borrow = require('../Model/Borrow');
const Reserve = require('../Model/Reserve');
const Favourite = require('../Model/Favourite');
const Preview = require('../Model/Preview');
const User = require('../Model/User');
const BookHelper = require('../Helper/BookHelper');
const generateZipForPath = require('../lib/generateZipForPath');
const PagePreview = require('../Model/PagePreview');
const { generateBorrowSummaryReport } = require('../src/service/book/borrowSummaryReport');

function regExpReplaceSpecialCharacters(str) {
  return str.replace(/[-[/\]{}()*+?.,\\^$|#]/g, '\\$&');
}

app.get('/sync-borrowed-books', async (req, res) => {
  const message = 'Borrowed book return cron will run every day at 12:00 AM on ' + new Date();
  let output = '';
  await Borrow.aggregate([
    {
      $match: { returned: false }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user_id',
        foreignField: '_id',
        as: 'users'
      }
    },
    {
      $lookup: {
        from: 'books',
        localField: 'book_id',
        foreignField: '_id',
        as: 'books'
      }
    },
    {
      $group: { _id: { user_id: '$user_id', book_id: '$book_id' }, count: { $sum: 1 }, borrow_id: { $first: '$_id' }, offset: { $first: '$users.offset' }, user_email: { $first: '$users.email' }, book_title: { $first: '$books.title' }, book_deleted: { $first: '$books.is_deleted' }, issue_date: { $first: '$issue_date' }, reborrowed_once: { $first: '$reborrowed_once' }, reborrowed_twice: { $first: '$reborrowed_twice' }, user_id: { $first: '$user_id' }, book_id: { $first: '$book_id' }, preview_book: { $first: '$books.preview_book' }, cover_photo: { $first: '$books.cover_photo' }, book_pdf: { $first: '$books.book_pdf' } }
    }
  ], async function (err, result) {
    if (err) throw err;
    if (!err && result.length) {
      result.forEach(async (el) => {
        const offset = el.offset;
        const temp = new Date(el.issue_date);
        const userTimezoneOffset = parseInt(offset) * 60000;
        const newDate = new Date(temp.getTime() - userTimezoneOffset);
        if (el.reborrowed_twice === true) {
          newDate.setDate(newDate.getDate() + 21);
        } else if (el.reborrowed_once === true) {
          newDate.setDate(newDate.getDate() + 14);
        } else {
          newDate.setDate(newDate.getDate() + 7);
        }
        newDate.setHours(23, 59, 0, 0);
        const today = new Date();
        const newDatetoday = new Date(today.getTime() - userTimezoneOffset);
        const user_email = el.user_email ? el.user_email[0] : el.email;
        const book_title = el.book_title ? el.book_title[0] : '';

        const preview_book = el.preview_book ? el.preview_book[0] : '';
        const book_pdf = el.book_pdf ? el.book_pdf[0] : '';
        const cover_photo = el.cover_photo ? el.cover_photo[0] : '';
        const book_deleted = el.book_deleted ? el.book_deleted[0] : '';

        if (new Date(newDate) <= new Date(newDatetoday)) {
          Borrow.findOneAndUpdate({
            _id: mongoose.Types.ObjectId(el.borrow_id)
          }, {
            returned: true,
            return_date: today
          }, {
            useFindAndModify: false
          }, () => {
            (BookHelper.updateAvailableStock(el.book_id, 'return'));
            const mailOptions = {
              to: user_email,
              from: '<EMAIL>',
              subject: 'Book Returned : Enrich Culture Group',
              text: 'Hi, Your Borrowed Book "' + book_title + '" is returned automatically because your Return Date is Over.'
            };
            // mailer(mailOptions);

            output += 'Mail Send to ' + user_email + '. Message : Hi, Your Borrowed Book "' + book_title + '" is returned automatically because your Return Date is Over.';
          }).catch((err) => {
            console.log(err);
          });
        }

        // Book should not be deleted if someone already borrowed the book
        const countsborrow = await Borrow.find({ book_id: mongoose.Types.ObjectId(el.book_id), is_deleted: false, returned: false });
        if (countsborrow.length === 0 && book_deleted === true) {
          if (fs.existsSync(preview_book)) {
            fs.unlink(preview_book, (err) => {
              if (err) {
                return res.json({
                  code: 500,
                  message: 'Internal error at delete preview',
                  success: false
                });
              }
            });
          }
          if (fs.existsSync(book_pdf)) {
            fs.unlink(book_pdf, (err) => {
              if (err) {
                return res.json({
                  code: 500,
                  message: 'Internal error at delete book pdf',
                  success: false
                });
              }
            });
          }
          if (fs.existsSync(cover_photo)) {
            fs.unlink(cover_photo, (err) => {
              if (err) {
                return res.json({
                  code: 500,
                  message: 'Internal error at delete cover',
                  success: false
                });
              }
            });
          }
        }

      });
    }
  });
  return res.send({
    code: 200,
    data: output,
    message: message
  });
});

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    const isbn_no = req.body.isbn_no;
    const fieldname = file.fieldname;
    const extension = path.extname(file.originalname);
    let temp = isbn_no + extension;
    if (fieldname === 'preview_book') {
      temp = isbn_no + '_preview' + extension;
    }
    temp = temp.split('#');
    temp = temp.join('_');
    cb(null, temp);
  },
  onError: function (err, next) {
    console.error('Multer error:', err);
    // Note: res is not available in this context, should handle differently
  }
});

const storagebulk = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {

    let temp = file.originalname;
    temp = temp.split('#');
    temp = temp.join('_');
    cb(null, temp);

  },
  onError: function (err, next) {
    console.error('Multer bulk error:', err);
    // Note: res is not available in this context, should handle differently
  }
});

const fileFilter = (req, file, cb) => {
  if (
    file.mimetype === 'image/jpeg' ||
		file.mimetype === 'image/png' ||
		file.mimetype === 'image/jpg'
  ) {
    cb(null, true);
  } else if (file.mimetype === 'application/pdf') {
    cb(null, true);
  } else if (file.mimetype === 'application/epub+zip') {
    cb(null, true);
  } else {
    cb(null, false);
  }
};

const bulkFileFilter = (req, file, cb) => {
  fs.access(`uploads/${file.originalname}`, fs.constants.F_OK, (err) => {
    console.log('bulk file filter error:', err);
    if (!err) cb(null, false);
    else {
      if (
        file.mimetype === 'image/jpeg' ||
				file.mimetype === 'image/png' ||
				file.mimetype === 'image/jpg'
      ) {
        cb(null, true);
      } else if (file.mimetype === 'application/pdf') {
        cb(null, true);
      } else if (file.mimetype === 'application/epub+zip') {
        cb(null, true);
      } else {
        cb(null, false);
      }
    }
  });
};

const upload = multer({
  storage: storage,
  limits: {
    // fileSize: 1024 * 1024 * 100,
  },
  fileFilter: fileFilter
});

const uploadbulk = multer({
  storage: storagebulk,
  limits: {
    // fileSize: 1024 * 1024 * 100,
  },
  fileFilter: fileFilter
});

app.use('/*', (req, res, next) => next());

app.post('/getBannersListing', async function (req, res) {
  const collection_type = req.header('x-current-collection') || 'JYG';

  const banner = await Banner.find({ collection_type }).sort({ _id: -1 });
  if (banner)
    return res.json({ code: 200, data: banner, message: '' });

  return res.json({ code: 422, message: 'Banners Not Available.' });
});

app.get('/getzipdata', async (req, res) => {
  const zip = await generateZipForPath('lib');
  res.send(zip);
});

const { getDashboardCounts } = require('../src/service/admin/getDashboardCounts');

/** Optimised code : 04-01-2022 **/
app.post('/getDashboardCounts', async (req, res) => {
  try {
    const collection_type = req.header('x-current-collection') || 'JYG';

    const startDate = new Date(req.body.startDate);
    startDate.setHours(0);
    startDate.setMinutes(0);
    startDate.setSeconds(0);

    const endDate = new Date(req.body.endDate);
    endDate.setDate(endDate.getDate() + 1);
    endDate.setHours(0);
    endDate.setMinutes(0);
    endDate.setSeconds(0);

    const email = req.body.email;

    // 调用服务层获取统计数据
    const dashboardData = await getDashboardCounts({
      startDate,
      endDate,
      email,
      collection_type
    });

    return res.json({
      code: 200,
      data: dashboardData,
      message: 'Operation Successful'
    });
  } catch (error) {
    console.error('getDashboardCounts error:', error);
    return res.json({
      code: 500,
      message: error.message || 'Internal server error',
      status: false
    });
  }
});

// Added on 28-09-2020
app.post('/returnBooks', async (req, res) => {
  const today = new Date();
  const date = today.toString();
  await Borrow.find({
    returned: false
  }).then((resx) => {
    if (!resx) {
      return res.json({
        code: 422,
        message: 'No data found',
        status: false
      });
    }
    let success = 0;
    let failed = 0;
    resx.forEach((el) => {
      if (new Date(el.return_date) <= today) {

        Borrow.findOneAndUpdate({
          _id: mongoose.Types.ObjectId(el._id)
        }, {
          returned: true,
          return_date: new Date()
        }, {
          useFindAndModify: false
        }, (err, updated) => {
          (BookHelper.updateAvailableStock(mongoose.Types.ObjectId(el.book_id), 'return'));
          User.findOne({
            _id: mongoose.Types.ObjectId(el.user_id)
          }).then((userData) => {
            if (userData) {
              const email = userData.email;
              const name = userData.name;
              Book.findOne({
                _id: el.book_id
              }).then(async (bookData) => {
                if (email) {
                  const mailOptions = {
                    to: email,
                    from: '<EMAIL>',
                    subject: 'Book Returned : Enrich Culture Group',
                    text: 'Hi, Your Borrowed Book "' + bookData.title + '" is returned automatically because your Return Date is Over.'
                  };
                  // mailer(mailOptions);
                }
              });
            }
          });
          success++;
        }).catch((err) => {
          failed++;
        });
      }
    });
    return res.json({
      code: 200,
      message: success + ' Books Returned Successfully. ' + failed + ' Return Failed.',
      status: false
    });
  });

});

app.post('/create',
  AdminHelper,
  upload.fields([{
    name: 'cover_photo',
    maxCount: 1
  },
  {
    name: 'book_pdf',
    maxCount: 1
  },
  {
    name: 'preview_book',
    maxCount: 1
  }
  ]),
  async (req, res) => {
    const collection_type = req.header('x-current-collection') || 'JYG';

    try {
      if (!req.files.cover_photo || !req.files.book_pdf) {
        res.json({
          code: 422,
          message: 'Please Upload Cover Photo only in .jpg or .png format & Book in .pdf or .epub format ',
          success: false
        });
        return res.end();
      }

      const mToken = req.header('SESSION-TOKEN');
      let added_by = '';
      await AdminSession.findOne({
        token: mToken
      }).then((resx) => {
        if (resx) {
          added_by = resx.admin_id;
        }
      });

      if (
        !req.body.category_id ||
				!req.body.title ||
				!req.body.excerpt ||
				!req.body.stock_quantity ||
				!req.body.author ||
				//!req.body.total_pages ||
				//!req.body.cost||
				!req.body.isbn_no
      ) {
        res.json({
          code: 422,
          message: 'Missing fields in request',
          success: false
        });
        return res.end();
      }
      const cover_photo = req.files.cover_photo[0];
      const book_pdf = req.files.book_pdf[0];
      let preview_book = req.files.preview_book[0];

      let book_recomm = false;
      let file_preview_name = 'uploads/' + req.body.isbn_no + '_preview.pdf';
      const book_name_path = book_pdf.path;
      const filename = book_pdf.filename;
      if (book_pdf.mimetype === 'application/pdf') {
        if (filename.includes('preview') === false) {
          await PDFNet.initialize();
          const filename_upload = process.env.UPLOAD_path + filename;
          const doc = await PDFNet.PDFDoc.createFromURL(filename_upload);
          const i = await doc.getPageIterator();
          let counts = 0;
          for (i; await i.hasNext(); i.next()) {
            counts++;
          }
          if (counts > 30) {
            doc.initSecurityHandler();
            let pageNum = await doc.getPageCount();
            while (pageNum > 30) {
              const itr = await doc.getPageIterator(pageNum);
              doc.pageRemove(itr);
              pageNum -= 1;
            }
          }
          file_preview_name = filename.replace('.pdf', '_preview.pdf');
          await doc.save('uploads/' + file_preview_name, PDFNet.SDFDoc.SaveOptions.e_linearized);
          PDFNet.shutdown();
        }
      }
      if (book_pdf.mimetype === 'application/epub+zip') {
        // book_name_path = '/bibi/?book=__samples/'+filename;
        if (fs.existsSync(process.env.EPUB_VIEWER_PATH + filename)) {
          fs.unlink(process.env.EPUB_VIEWER_PATH + filename, (err) => {
            if (err) {
              return res.json({
                code: 500,
                message: 'Internal error at updating Preview book',
                success: false
              });
            }
          });
        }

        /* After uploading the epub file, move it to its desired location to load in the browser
				var oldPath = 'uploads/'+filename;
				var newPath = process.env.EPUB_VIEWER_PATH+filename;
				fs.rename( oldPath, newPath, function (err) {
					if (err){
						console.log(err);
					}else{
						console.log('Successfully moved!');
					}
				}); */
      }


      if (preview_book.mimetype === 'application/epub+zip') {
        const filename = preview_book.filename;
        // file_preview_name = '/bibi/?book=__samples/'+filename;
        if (fs.existsSync(process.env.EPUB_VIEWER_PATH + filename)) {
          fs.unlink(process.env.EPUB_VIEWER_PATH + filename, (err) => {
            if (err) {
              return res.json({
                code: 500,
                message: 'Internal error at updating Preview book',
                success: false
              });
            }
          });
        }

        /* After uploading the epub file, move it to its desired location to load in the browser
				var oldPath = 'uploads/'+filename;
				var newPath = process.env.EPUB_VIEWER_PATH+filename;
				fs.rename( oldPath, newPath, function (err) {
					if (err){
						console.log(err);
					}else{
						console.log('Successfully moved!');
					}
				}); */
      }

      if (req.files.preview_book) { preview_book = 'uploads/' + req.files.preview_book[0].path; }
      if (req.body.book_recomm) {
        book_recomm = req.body.book_recomm;
      }

      const mBook = new Book({
        title: req.body.title,
        excerpt: req.body.excerpt,
        stock_quantity: req.body.stock_quantity,
        available_quantity: req.body.stock_quantity,
        publishingGroup: req.body.publishingGroup,
        imprints: req.body.imprints,
        author: req.body.author,
        //total_pages: req.body.total_pages,
        //cost: req.body.cost,
        isbn_no: req.body.isbn_no,
        cover_photo: cover_photo.path,
        book_pdf: book_name_path,
        preview_book: file_preview_name.indexOf('uploads') !== -1 ? file_preview_name : 'uploads/' + file_preview_name,
        added_by: added_by,
        book_recomm: book_recomm,
        publish_date: req.body.publish_date,
        added_at: new Date(),
        collection_type
      });

      try {
        const arr = req.body.category_id.split(',');
        arr.forEach((el) => {
          mBook.category_id.push(el);
        });
      } catch (error) {
        console.log(error.message);
        return res.json({
          code: 500,
          message: 'Internal error in split',
          status: false
        });
      }

      await mBook
        .save()
        .then((storedData) => {
          res.json({
            code: 200,
            message: 'Operation successful.',
            data: {
              id: storedData._id,
              category_id: storedData.category_id,
              title: storedData.title,
              excerpt: storedData.excerpt,
              available_quantity: storedData.available_quantity,
              stock_quantity: storedData.stock_quantity,
              author: storedData.author,
              //total_pages: storedData.total_pages,
              //cost: storedData.cost,
              cover_photo: req.files.cover_photo[0].path,
              book_pdf: req.files.book_pdf[0].path,
              preview_book: preview_book
            }
          });
          return res.end();
        })
        .catch((err) => {
          return res.json({
            code: 500,
            message: 'Internal error',
            status: false
          });
        });

    } catch (e) {
      console.log(e.message);
      res.json({
        code: 422,
        message: 'Something went wrong! Please try again later.',
        success: false
      });
      return res.end();
    }

  }
);

app.post('/createbulk', async (req, res) => {
  const collection_type = req.header('x-current-collection') || 'JYG';
  if (!req.body.data || !req.body.uid) {
    res.json({
      code: 422,
      message: 'Missing fields in request',
      success: false
    });
    return res.end();
  }

  if (req.body.data.length > 0) {
    const mToken = req.header('SESSION-TOKEN');
    let added_by = '';
    await AdminSession.findOne({
      token: mToken
    }).then((resx) => {
      if (resx) {
        added_by = resx.admin_id;
      }
    });

    const bookbulk = [];
    const catsName = [];
    const promises = req.body.data.map(async function (el) {
      const arr = el.category.split(',').map(x => x.trim());
      const promises1 = arr.map(async (obj) => {
        await Category.findOne({
          name: obj
        }).then(async (resx) => {
          if (resx) {
            // Category already exists, skip
          } else {
            catsName.push(obj);
          }
        });
      });
    });

    const catsinsert = [];
    setTimeout(async function () {
      const uniquecats = Array.from(new Set(catsName));

      const promises2 = uniquecats.map(async function (el) {
        const catshere = {
          name: el
        };
        catsinsert.push(catshere);
      });

      const options = {
        ordered: true
      };
      const resultcats = await Category.insertMany(catsinsert, options);

      const promises3 = req.body.data.map(async function (el) {

        const arr = el.category.split(',').map(x => x.trim());
        const catId = [];
        const promises4 = arr.map(async (obj) => {
          await Category.findOne({
            name: obj
          }).then(async (resx) => {
            if (resx) {
              catId.push(resx._id);
            }
          });
        });

        const book_type = (req.body.book_type);
        const results2 = await Promise.all(promises4);

        let preview_book = 'uploads/' + el.preview_book;
        if (el.preview_book === '') {
          preview_book = '';
        }

        /* Needs to update the path for the epub books */
        // var nameSplit = el.preview_book.split(".");
        // if(nameSplit.length > 1 && nameSplit[1]=="epub"){
        // preview_book = '/bibi/?book=__samples/' + el.preview_book;
        // }

        const book_pdf = 'uploads/' + el.book_pdf;
        // var nameSplit = el.book_pdf.split(".");
        // if(nameSplit.length > 1 && nameSplit[1]=="epub"){
        // book_pdf = '/bibi/?book=__samples/' + el.book_pdf;
        // }

        const mBook = {
          title: el.title,
          excerpt: el.excerpt,
          stock_quantity: el.stock_quantity,
          available_quantity: el.stock_quantity,
          author: el.author,
          cover_photo: 'uploads/' + el.cover_photo,
          book_pdf: book_pdf,
          preview_book: preview_book.indexOf('uploads') !== -1 ? preview_book : 'uploads/' + preview_book,
          added_by: added_by,
          book_recomm: el.ebook_recommended,
          added_at: new Date(),
          is_deleted: false,
          category_id: catId,
          publishingGroup: el.publishingGroup,
          imprints: el.imprints,
          isbn_no: el.ISBN_No,
          publish_date: el.publish_date,
          collection_type
        };

        await Book.findOne({
          isbn_no: el.ISBN_No,
          collection_type
        }).then((resx) => {
          if (resx) {
            Book.findOneAndUpdate({
              isbn_no: el.ISBN_No,
              collection_type
            }, {
              $set: mBook
            }).then((books) => {
            });
          } else {
            bookbulk.push(mBook);
          }
        });
      });
    }, 5000);
    setTimeout(async function () {
      if (bookbulk.length > 0) {
        const options = {
          ordered: true
        };
        const result = await Book.insertMany(bookbulk, options);
      }
      res.json({
        code: 200,
        message: 'Operation successful.',
        data: req.body.data.length
      });

      return res.end();
    }, 8000);
  } else {
    res.json({
      code: 200,
      message: 'Empty Data !',
      data: req.body.data.length
    });
    return res.end();
  }
});

app.post('/uploadbulk', uploadbulk.fields([{
  name: 'files'
}]), async (req, res) => {
  try {
    if (req.files.files) {
      await PDFNet.initialize();
      const promises = (req.files.files).map(async function callback(value, index) {
        const filename = req.files.files[index].filename;
        if (req.files.files[index].mimetype === 'application/pdf') {
          if (filename.includes('preview') === false) {
            const filename_upload = process.env.UPLOAD_path + filename;
            const doc = await PDFNet.PDFDoc.createFromURL(filename_upload);
            const i = await doc.getPageIterator();
            let counts = 0;
            for (i; await i.hasNext(); i.next()) {
              counts++;
            }
            if (counts > 30) {
              doc.initSecurityHandler();
              let pageNum = await doc.getPageCount();
              while (pageNum > 30) {
                const itr = await doc.getPageIterator(pageNum);
                doc.pageRemove(itr);
                pageNum -= 1;
              }
            }
            let file_preview_name = '';
            file_preview_name = filename.replace('.pdf', '_preview.pdf');
            await doc.save('uploads/' + file_preview_name, PDFNet.SDFDoc.SaveOptions.e_linearized);
          }
        }

        if (req.files.files[index].mimetype === 'application/epub+zip') {
          /* After uploading the epub file, move it to its desired location to load in the browser
					var oldPath = 'uploads/'+filename;
					var newPath = process.env.EPUB_VIEWER_PATH+filename;
					fs.rename( oldPath, newPath, function (err) {
						if (err){
							console.log(err);
						}else{
							console.log('Successfully moved!');
						}
					});
					*/
        }
      });
      const results = await Promise.all(promises);
      PDFNet.shutdown();
      res.json({
        code: 200,
        message: 'Operation successful.',
        data: req.files
      });
      return res.end();
    } else {
      res.json({
        code: 200,
        message: 'Operation successful.',
        data: req.files
      });
      return res.end();
    }
  } catch (e) {
    console.log('bulk upload - ', e.message);
    res.json({
      code: 422,
      message: 'Something went wrong! Please try again later.',
      success: false
    });
    return res.end();
  }
});

app.post('/delete', AdminHelper, upload.none(), async (req, res) => {
  if (!req.body.book_id) {
    res.json({
      code: 422,
      message: 'Missing fields in request',
      success: false
    });
    return res.end();
  }

  await Book.findOneAndUpdate({
    _id: mongoose.Types.ObjectId(req.body.book_id)
  }, {
    is_deleted: true,
    deleted_at: new Date()
  }, {
    useFindAndModify: false
  },
  async (err, updated) => {
    if (err) {
      return res.json({
        code: 500,
        message: 'Internal error at updating book-cover',
        success: false
      });
    }

    const countsborrow = await Borrow.find({
      is_deleted: false,
      returned: false,
      book_id: mongoose.Types.ObjectId(req.body.book_id)
    }).countDocuments();

    // Book should not be deleted if someone already borrowed the book
    if (countsborrow === 0) {
      if (fs.existsSync(updated.preview_book)) {
        fs.unlink(updated.preview_book, (err) => {
          if (err) {
            return res.json({
              code: 500,
              message: 'Internal error at delete preview',
              success: false
            });
          }
        });
      }
      if (fs.existsSync(updated.book_pdf)) {
        fs.unlink(updated.book_pdf, (err) => {
          if (err) {
            return res.json({
              code: 500,
              message: 'Internal error at delete book pdf',
              success: false
            });
          }
        });
      }
      if (fs.existsSync(updated.cover_photo)) {
        fs.unlink(updated.cover_photo, (err) => {
          if (err) {
            return res.json({
              code: 500,
              message: 'Internal error at delete cover',
              success: false
            });
          }
        });
      }

      return res.json({
        code: 200,
        message: 'Deletion successful'
      });
    } else {
      /* If book will be deleted, then it will send mail to all borrowed users, that this book will no longer be available for future borrow, and will be returned on XYZ date */
      /*
				await Borrow.aggregate([
					{
						$match : { returned: false, book_id: mongoose.Types.ObjectId(req.body.book_id) }
					},
					{
						$lookup: {
							from: 'users',
							localField: 'user_id',
							foreignField: '_id',
							as: 'users'
						}
					},
					{
						$lookup: {
							from: 'books',
							localField: 'book_id',
							foreignField: '_id',
							as: 'books'
						}
					},
					{
						$group : { _id: { user_id: "$user_id", book_id: "$book_id" }, borrow_id : { $first : "$_id" }, offset : { $first : "$users.offset" }, user_email: { $first : "$users.email" }, book_title: { $first : "$books.title" }, book_deleted: { $first : "$books.is_deleted" }, issue_date: { $first : "$issue_date" }, reborrowed_once: { $first : "$reborrowed_once" }, reborrowed_twice: { $first : "$reborrowed_twice" }, user_id:{ $first : "$user_id" }, book_id : { $first : "$book_id" }, preview_book : { $first : "$books.preview_book" }, cover_photo : { $first : "$books.cover_photo" } , book_pdf : { $first : "$books.book_pdf" } }
					}
				],async function (err, result) {
					if(err) throw err;
					if(!err && result.length){
						result.forEach(async (el) => {
							var offset = el.offset;
							var temp = new Date(el.issue_date);
							var userTimezoneOffset = parseInt(offset) * 60000;
							var newReturnDate = new Date(temp.getTime() - userTimezoneOffset);
							if (el.reborrowed_twice == true) {
								newReturnDate.setDate(newReturnDate.getDate() + 21);
							} else if (el.reborrowed_once == true) {
								newReturnDate.setDate(newReturnDate.getDate() + 14);
							} else {
								newReturnDate.setDate(newReturnDate.getDate() + 7);
							}
							newReturnDate.setHours(23, 59, 0, 0);
							let user_email = el.user_email ? el.user_email[0] : el.email;
							let book_title = el.book_title ? el.book_title[0] : '';

							var dateObj = newReturnDate.toString();
							var dateArr = dateObj.split(' ');
							var return_date = dateArr[0] + ', ' + dateArr[1] + ' ' + dateArr[2] + ' ' + dateArr[3];

							mailOptions = {
								to: user_email,
								from: '<EMAIL>',
								subject: 'Book "' + book_title + '" Offself: Enrich Culture Group',
								text: 'Hi, Your Borrowed Book "' + book_title + '" in the Hong Kong Public Library e-book has been off-self from our library, You cam read it though your account till '+ return_date +'. After that it will be auto returned from your account.'
							};
							mailer(mailOptions);
						});
					}
				});
				*/
      /* If book will be deleted, then it will also removed from reserve list for all users, and they will get a email that book is no available */

      const results = await Reserve.aggregate([
        {
          $match: { book_id: mongoose.Types.ObjectId(req.body.book_id), is_deleted: false }
        },
        {
          $lookup: {
            from: 'books',
            localField: 'book_id',
            foreignField: '_id',
            as: 'books'
          }
        },
        {
          $group: { '_id': { 'book_id': '$book_id' }, 'reserve': { $push: { 'id': '$_id', 'user_id': '$user_id', 'email': '$email' } } }
        }
      ]);

      if (results.length > 0) {
        const book_id = results[0]._id.book_id;
        results[0].reserve.forEach(async (resobj) => {
          await Reserve.updateOne({
            _id: mongoose.Types.ObjectId(resobj.id)
          }, {
            $set: {
              is_deleted: true
            }
          },
          async (err, updated) => {
            // let doc = await Favourite.findOneAndDelete({
            // user_id : mongoose.Types.ObjectId(resobj.user_id),
            // book_id : mongoose.Types.ObjectId(book_id)
            // });

            // Book.findOne({
            // _id: mongoose.Types.ObjectId(book_id)
            // }).then((bookData) => {
            // mailOptions = {
            // to: resobj.email,
            // from: '<EMAIL>',
            // subject: 'Your Reserved book ' + bookData.title + ' is off-self.',
            // html: 'Dear reader, <br/><br/>The Book "'+ bookData.title +'" that you reserved in the Hong Kong Public Library e-book has been off-self from our library, You can log in to the <a href="http://library-connect.com/store">e-books service</a> of the Hong Kong Public Library and check some other related books.<br/><br/>Thank you for using Ebook service.'
            // };
            // mailer(mailOptions);
            // });
          });
        });
      }

      return res.json({
        code: 200,
        message: 'Deletion successful'
      });
    }
  });
});

app.post('/detail', upload.none(), async (req, res) => {
  const mToken = req.header('SESSION-TOKEN');
  const countryCode = req.header('CF-IPCountry');
  // 设置响应头返回 countryCode
  res.set('X-Country-Code', countryCode || '');
  let temp = true;
  let ids = '';
  if (mToken) {
    await AdminSession.findOne({
      token: mToken
    }).then((resx) => {
      if (resx) {
        if (resx.is_active) {
          temp = false;
        }
      }
    });
    await UserSession.findOne({
      token: mToken
    }).then((resxx) => {
      if (resxx) {
        if (resxx.is_active) {
          temp = false;
        }
        ids = resxx.user_id;
      }
    });
  }
  if (temp) {
    if (!req.body.id) {
      res.json({
        code: 422,
        message: 'Missing fields in request',
        success: false
      });
      return res.end();
    }

    Book.findOne({
      _id: mongoose.Types.ObjectId(req.body.id),
      $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
    }).populate('category_id').then(async (resx) => {
      if (!resx) {
        res.json({
          code: 422,
          message: 'No books found',
          status: false
        });
        return res.end();
      }

      const category_id = resx.category_id;
      const catResp = [];
      const promises1 = category_id.map(async function (el) {
        const cat_id = el._id;
        let countcats = 0;
        await Book.find({
          category_id: {
            $in: cat_id
          },
          _id: {
            $ne: req.body.id
          },
          $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
        }).limit(10).then(async (catRes) => {
          if (catRes && catRes !== null) {
            catRes.forEach(function (els) {
              catResp.forEach(function (ell) {
                if ((ell._id).toString().trim() === (els._id).toString().trim()) {
                  countcats++;
                }
              });
              if (countcats === 0) {
                catResp.push(els);
              }
            });
          }
        });
      });

      const results1 = await Promise.all(promises1);
      if (!await (BookHelper.isBookInStock(req.body.id))) {
        res.json({
          code: 200,
          data: {
            book: resx,
            catResp: catResp,
            borrowed: false,
            is_stockout: true
          },
          message: 'Operation successful.'
        });
        return res.end();
      } else {
        res.json({
          code: 200,
          data: {
            book: resx,
            catResp: catResp,
            borrowed: false,
            is_stockout: false
          },
          message: 'Operation successful.'
        });
        return;
      }
    });
  } else {
    if (!req.body.id || !req.body.user_id) {
      res.json({
        code: 422,
        message: 'Missing fields in request',
        success: false
      });
      return res.end();
    }

    Book.findOne({
      _id: mongoose.Types.ObjectId(req.body.id),
      $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
    })
      .populate('category_id')
      .then(async (resx) => {
        if (!resx) {
          res.json({
            code: 422,
            message: 'No books found',
            status: false
          });
          return res.end();
        }
        const category_id = resx.category_id;
        const catResp = [];
        let fav_data = false;
        const countcats = 0;
        const promises1 = category_id.map(async function (el) {
          const cat_id = el._id;
          let countcats = 0;
          await Book.find({
            category_id: {
              $in: cat_id
            },
            _id: {
              $ne: req.body.id
            },
            $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
          }).limit(10).then(async (catRes) => {
            if (catRes && catRes !== null) {
              catRes.forEach(function (els) {
                catResp.forEach(function (ell) {
                  if ((ell._id).toString().trim() === (els._id).toString().trim()) {
                    countcats++;
                  }
                });
                if (countcats === 0) {
                  catResp.push(els);
                }
              });
            }
          });
        });

        const results1 = await Promise.all(promises1);
        if (await BookHelper.isBookFav(req.body.user_id, req.body.id)) {
          Favourite.findOne({
            user_id: req.body.user_id,
            book_id: req.body.id
          }).then(async (favres) => {
            if (favres) {
              fav_data = favres;
            }
            if (await BookHelper.isBookBorrowed(req.body.user_id, req.body.id)) {
              res.json({
                code: 200,
                data: {
                  book: resx,
                  catResp: catResp,
                  borrowed: true,
                  fav: true,
                  favdata: fav_data
                },
                message: 'Operation successful.'
              });
              return res.end();
            } else {
              if (!(await BookHelper.isBookInStock(req.body.id))) {
                res.json({
                  code: 200,
                  data: {
                    book: resx,
                    catResp: catResp,
                    borrowed: false,
                    is_stockout: true,
                    fav: true,
                    favdata: fav_data
                  },
                  message: 'Operation successful.'
                });
                return res.end();
              } else {
                const stock = await BookHelper.isBookInStock(req.body.id);
                if (stock > 0) {
                  res.json({
                    code: 200,
                    data: {
                      book: resx,
                      catResp: catResp,
                      borrowed: false,
                      is_stockout: false,
                      fav: true,
                      favdata: fav_data
                    },
                    message: 'Operation successfulyyy.'
                  });
                  return res.end();
                } else {
                  res.json({
                    code: 200,
                    data: {
                      book: resx,
                      catResp: catResp,
                      borrowed: false,
                      is_stockout: true,
                      fav: true,
                      favdata: fav_data
                    },
                    message: 'Operation successful.'
                  });
                  return res.end();
                }
              }
            }
          });

        } else {
          if (await BookHelper.isBookBorrowed(req.body.user_id, req.body.id)) {
            res.json({
              code: 200,
              data: {
                book: resx,
                catResp: catResp,
                borrowed: true,
                fav: false
              },
              message: 'Operation successful.'
            });
            return res.end();
          } else {
            if (!(await BookHelper.isBookInStock(req.body.id))) {
              res.json({
                code: 200,
                data: {
                  book: resx,
                  catResp: catResp,
                  borrowed: false,
                  is_stockout: true,
                  fav: false
                },
                message: 'Operation successful.'
              });
              return res.end();
            } else {
              const stock = await BookHelper.isBookInStock(req.body.id);
              if (stock > 0) {
                res.json({
                  code: 200,
                  data: {
                    book: resx,
                    catResp: catResp,
                    borrowed: false,
                    is_stockout: false,
                    fav: false,
                    favdata: fav_data
                  },
                  message: 'Operation successful.'
                });
                return res.end();
              } else {
                res.json({
                  code: 200,
                  data: {
                    book: resx,
                    catResp: catResp,
                    borrowed: false,
                    is_stockout: true,
                    fav: false,
                    favdata: fav_data
                  },
                  message: 'Operation successful.'
                });
                return res.end();
              }
            }
          }
        }
      });
  }
});

app.post('/addtopreview', upload.none(), async (req, res) => {
  if (!req.body.book_id || !req.body.issue_date) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }
  const book_id = req.body.book_id;
  const preview = Preview({
    book_id: req.body.book_id,
    created_on: (req.body.issue_date).toString()
  });
  await preview.save().then((stored) => {
    return res.json({
      code: 200,
      data: stored,
      message: 'Operation successful.'
    });
  })
    .catch((err) => {
      return res.json({
        code: 500,
        message: 'Internal Error in add to preview.',
        status: false
      });
    });
});



app.post('/countPagePreview', async (req, res) => {
  const collection_type = req.header('x-current-collection');
  if (!req.body.startDate || !req.body.endDate) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }

  let condition = {
    created_on: {
      $gte: new Date(Number(req.body.startDate) * 1000),
      $lte: new Date(Number(req.body.endDate) * 1000)
    }
  };
  if (req.body.page)
    condition = {
      ...condition, collection_type: collection_type || null,
      page: req.body.page
    };

  if (collection_type)
    condition = {
      ...condition, collection_type: collection_type
    };

  const count = await PagePreview.countDocuments(condition);

  return res.json({
    code: 200,
    data: {
      count
    },
    message: 'Operation successful.'
  });
});


app.post('/addPagePreview', async (req, res) => {
  const collection_type = req.header('x-current-collection');

  if (!req.body.page) {
    return res.json({
      code: 422,
      message: 'Missing required fields: page',
      status: false
    });
  }
  if (collection_type) {
    const pagePreview = PagePreview({
      page: req.body.page,
      collection_type: collection_type,
      created_on: new Date()
    });

    await pagePreview.save().then((stored) => {
      return res.json({
        code: 200,
        data: stored,
        message: 'Operation successful.'
      });
    })
      .catch((err) => {
        return res.json({
          code: 500,
          message: 'Internal Error in add to preview.',
          status: false
        });
      });
  } else {
    const jygPagePreview = PagePreview({
      page: req.body.page,
      collection_type: 'JYG',
      created_on: new Date()
    });
    await jygPagePreview.save();

    const lhzPagePreview = PagePreview({
      page: req.body.page,
      collection_type: 'LHZ',
      created_on: new Date()
    });
    await lhzPagePreview.save();

    const xstdPagePreview = PagePreview({
      page: req.body.page,
      collection_type: 'XSTD',
      created_on: new Date()
    });
    await xstdPagePreview.save();

    return res.json({
      code: 200,
      data: [jygPagePreview, lhzPagePreview, xstdPagePreview],
      message: 'Operation successful.'
    });
  }

});

app.post('/checkborrow', upload.none(), async (req, res) => {
  const mToken = req.header('SESSION-TOKEN');
  let temp = true;
  const temp1 = true;
  let ids = '';
  const admin = req.body.admin;
  await AdminSession.findOne({
    token: mToken
  }).then(async (resx) => {
    if (resx) {

      temp = false;
    } else {
      if (admin === 'admin') {
        temp = false;
      } else {
        await UserSession.findOne({
          token: mToken,
          is_active: true
        }).then((resx) => {
          if (resx) {
            ids = resx.user_id;
          } else {
            res.json({
              code: 900,
              message: 'user logout',
              success: false
            });
            return res.end();
          }
        });
      }
    }
  });

  if (temp && temp1) {

    if (!req.body.id) {
      res.json({
        code: 422,
        message: 'Missing fields in request',
        success: false
      });
      return res.end();
    }

    Book.findOne({
      _id: mongoose.Types.ObjectId(req.body.id)
    })
      .populate('category_id')
      .then(async (resx) => {
        if (resx.length === 0) {
          res.json({
            code: 422,
            message: 'Provided invalid id',
            status: false
          });
          return res.end();
        }

        const category_id = resx.category_id;
        const catResp = [];
        category_id.forEach(function (el) {
          const cat_id = el._id;
          Book.findOne({
            category_id: {
              $in: cat_id
            },
            _id: {
              $ne: req.body.id
            }
          }).then(async (catRes) => {
            if (catRes && catRes !== null) {
              catResp.push(catRes);
            }
          });
        });

        const borrowed = await BookHelper.isBookBorrowed(ids, req.body.id);
        const stock = await BookHelper.isBookInStock(req.body.id);
        const is_stockout = stock <= 0;

        res.json({
          code: 200,
          message: borrowed ? 'You\'ve already borrowed this!' : 'Operation successful.',
          data: {
            book: resx,
            catResp: catResp,
            borrowed,
            is_stockout
          }
        });
        return res.end();
      });
  } else {

    if (!req.body.id) {
      res.json({
        code: 422,
        message: 'Missing fields in request',
        success: false
      });
      return res.end();
    }

    Book.findOne({
      _id: mongoose.Types.ObjectId(req.body.id),
      $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
    })
      .populate('category_id')
      .then(async (resx) => {
        if (resx.length === 0) {
          res.json({
            code: 422,
            message: 'Provided invalid id',
            status: false
          });
          return res.end();
        }

        const category_id = resx.category_id;
        const catResp = [];
        category_id.forEach(function (el) {
          const cat_id = el._id;
          Book.findOne({
            category_id: {
              $in: cat_id
            },
            _id: {
              $ne: req.body.id
            }
          }).then(async (catRes) => {
            if (catRes && catRes !== null) {
              catResp.push(catRes);
            }
          });
        });


        const stock = await BookHelper.isBookInStock(req.body.id);
        const is_stockout = stock <= 0;

        res.json({
          code: 200,
          data: {
            book: resx,
            catResp: catResp,
            borrowed: true,
            is_stockout
          },
          message: 'Operation successful.'
        });
        return res.end();
      });
  }

});

async function bookQtyUpdate(bookId) {
  // 使用 BookHelper.updateAvailableStock 来统一管理库存
  // 这个函数现在已经被 BookHelper.isBookInStock 替代，建议直接调用 BookHelper 方法
  console.log(`bookQtyUpdate called for book ${bookId}, delegating to BookHelper.isBookInStock`);

  // 调用 BookHelper.isBookInStock 会自动重新计算库存（如果需要的话）
  const stock = await BookHelper.isBookInStock(bookId);
  console.log(`Book ${bookId} stock recalculated: ${stock}`);
  return true;
}

/**
 * @route POST /get
 * @description Get books with optional filtering, sorting and searching
 * @access Public
 */
app.post('/get', async (req, res) => {
  try {
    // NOTE
    const result = await getBooks(req);

    if (result.useFilter) {
      // Use the filterRecords function for filtered queries
      await filterRecords(
        res,
        result.match,
        result.sortBy || 'added_at',
        result.sortByType || 'desc',
        req.header('x-current-collection')
      );
    } else {
      // Return direct result (for email-based search)
      res.json(result);
    }
  } catch (error) {
    console.error('Error in /get route:', error);
    res.status(500).json({
      code: 500,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

app.post('/getRecommended', async (req, res) => {
  const collection_type = req.header('x-current-collection');
  const books = await BookHelper.recommendedBooks(
    req.body.book_id,
    collection_type
  );
  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/getRecommendedbydate', async (req, res) => {
  const collection_type = req.header('x-current-collection');
  const startDate = new Date(req.body.startDate);
  startDate.setHours(0);
  startDate.setMinutes(0);
  startDate.setSeconds(0);

  const endDate = new Date(req.body.endDate);
  endDate.setDate(endDate.getDate() + 1);
  endDate.setHours(0);
  endDate.setMinutes(0);
  endDate.setSeconds(0);

  const books = await (BookHelper.recommendedBooksbydate(req.body.book_id, startDate, endDate, req.body.all, req.body.email, true, collection_type));
  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/getRenewedbydate', async (req, res) => {
  const collection_type = req.header('x-current-collection');

  const startDate = moment.utc(req.body.startDate).tz('Asia/Hong_Kong').startOf('day').toDate();
  const endDate = moment.utc(req.body.endDate).tz('Asia/Hong_Kong').endOf('day').toDate();
  // let startDate = new Date(req.body.startDate);
  // startDate.setHours(0);
  // startDate.setMinutes(0);
  // startDate.setSeconds(0);

  // let endDate = new Date(req.body.endDate);
  // endDate.setDate(endDate.getDate() + 1);
  // endDate.setHours(0);
  // endDate.setMinutes(0);
  // endDate.setSeconds(0);

  const books = await BookHelper.renewBooksbydateV3(
    startDate,
    endDate,
    req.body.email,
    collection_type
  );


  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/getReservedbydate', async (req, res) => {
  const collection_type = req.header('x-current-collection');
  const startDate = new Date(req.body.startDate);
  startDate.setHours(0);
  startDate.setMinutes(0);
  startDate.setSeconds(0);

  const endDate = new Date(req.body.endDate);
  endDate.setDate(endDate.getDate() + 1);
  endDate.setHours(0);
  endDate.setMinutes(0);
  endDate.setSeconds(0);

  const books = await BookHelper.reservedBooksbydateV2(req.body.book_id, startDate, endDate, req.body.all, req.body.email, collection_type);
  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

// app.post("/getunusedBookBydate", async (req, res) => {
// 	const collection_type = req.header("x-current-collection");
//     let startDate = new Date(req.body.startDate);
// 	startDate.setHours(0);
// 	startDate.setMinutes(0);
// 	startDate.setSeconds(0);

//     let endDate = new Date(req.body.endDate);
// 	endDate.setDate(endDate.getDate() + 1);
// 	endDate.setHours(0);
// 	endDate.setMinutes(0);
// 	endDate.setSeconds(0);

//     const books = await BookHelper.getunusedBookBydate(req.body.book_id, startDate, endDate, req.body.all, req.body.email, collection_type);
//     return res.json({
//         code: 200,
//         data: books,
//         message: "Operation Successful",
//     });
// });

app.post('/getunusedBookBydate', async (req, res) => {
  const collection_type = req.header('x-current-collection');
  const books = await BookHelper.getunusedBookByDateV2(
    collection_type,
    req.body.startDate,
    req.body.endDate,
    req.body.email
  );
  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/getpreviewsbydate', async (req, res) => {
  const collection_type = req.header('x-current-collection');
  const startDate = new Date(req.body.startDate);
  startDate.setHours(0);
  startDate.setMinutes(0);
  startDate.setSeconds(0);

  const endDate = new Date(req.body.endDate);
  endDate.setDate(endDate.getDate() + 1);
  endDate.setHours(0);
  endDate.setMinutes(0);
  endDate.setSeconds(0);

  const books = await BookHelper.previewBooksbydate(req.body.book_id, startDate, endDate, req.body.all, req.body.email, collection_type);
  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/getRecommBooksbydate', async (req, res) => {
  const collection_type = req.header('x-current-collection');
  // let startDate = new Date(req.body.startDate);
  // startDate.setHours(0);
  // startDate.setMinutes(0);
  // startDate.setSeconds(0);

  // let endDate = new Date(req.body.endDate);
  // endDate.setDate(endDate.getDate() + 1);
  // endDate.setHours(0);
  // endDate.setMinutes(0);
  // endDate.setSeconds(0);

  // const books = await BookHelper.recommendedBooksbydate(req.body.book_id, startDate, endDate, req.body.all, req.body.email, true);
  const books = await BookHelper.recommBooksbydate(req.body.book_id, req.body.email, collection_type);
  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/getAllbydate', async (req, res) => {
  const collection_type = req.header('x-current-collection');
  const startDate = new Date(req.body.startDate);
  startDate.setHours(0);
  startDate.setMinutes(0);
  startDate.setSeconds(0);

  const endDate = new Date(req.body.endDate);
  endDate.setDate(endDate.getDate() + 1);
  endDate.setHours(0);
  endDate.setMinutes(0);
  endDate.setSeconds(0);

  const books = await BookHelper.AllBooksbydate(req.body.book_id, startDate, endDate, req.body.all, req.body.email, collection_type);
  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});
app.post('/changerecommtrue', AdminHelper, async (req, res) => {
  Book.findOneAndUpdate({
    _id: req.body.book_id
  }, {
    $set: {
      book_recomm: false
    }
  })
    .then((books) => {
      res.json({
        code: 200,
        data: books,
        message: 'Operation successful.'
      });
    })
    .catch((err) => {
      res.json({
        message: 'Internal error in change recomm.',
        success: false,
        code: 500
      });
    });
});
app.post('/changerecommfalse', AdminHelper, async (req, res) => {
  Book.findOneAndUpdate({
    _id: req.body.book_id
  }, {
    $set: {
      book_recomm: true
    }
  })
    .then((books) => {
      res.json({
        code: 200,
        data: books,
        message: 'Operation successful.'
      });
    })
    .catch((err) => {
      res.json({
        message: 'Internal error in change recomm.',
        success: false,
        code: 500
      });
    });
});

app.post('/getRecommendedBookstop', async (req, res) => {
  if (!req.body.startDate || !req.body.endDate) {
    return res.json({
      code: 422,
      message: 'Missing required fields: startDate, endDate',
      status: false
    });
  }
  const collection_type = req.header('x-current-collection');
  const email = (req.body.email);
  const startDate = moment.utc(req.body.startDate).tz('Asia/Hong_Kong').startOf('day').toDate();
  const endDate = moment.utc(req.body.endDate).tz('Asia/Hong_Kong').endOf('day').toDate();
  /*let startDate = '';
	let endDate = '';

	if (req.body.startDate) {
		startDate = new Date(req.body.startDate);
		startDate.setHours(0);
		startDate.setMinutes(0);
		startDate.setSeconds(0);

		endDate = new Date(req.body.endDate);
		endDate.setDate(endDate.getDate() + 1);
		endDate.setHours(0);
		endDate.setMinutes(0);
		endDate.setSeconds(0);
	} else {
		startDate = new Date();
		startDate.setHours(0);
		startDate.setMinutes(0);
		startDate.setSeconds(0);

		endDate = new Date();
		endDate.setDate(endDate.getDate() + 1);
		endDate.setHours(0);
		endDate.setMinutes(0);
		endDate.setSeconds(0);
	}*/

  const isPetronVisible = false;
  const books = await BookHelper.recommendedBooksbydateV2(req.body.book_id || 'unknown', startDate, endDate, 0, email, isPetronVisible, collection_type);

  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/getRenewedBookstoday', async (req, res) => {
  const email = (req.body.email);

  const startDate = new Date();
  startDate.setHours(0);
  startDate.setMinutes(0);
  startDate.setSeconds(0);

  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 1);
  endDate.setHours(0);
  endDate.setMinutes(0);
  endDate.setSeconds(0);

  const books = await BookHelper.renewedBooksbydate(req.body.book_id, startDate, endDate, 0, email);

  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/getReservedBookstoday', async (req, res) => {
  const email = (req.body.email);

  const startDate = new Date();
  startDate.setHours(0);
  startDate.setMinutes(0);
  startDate.setSeconds(0);

  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 1);
  endDate.setHours(0);
  endDate.setMinutes(0);
  endDate.setSeconds(0);

  const books = await BookHelper.reservedBooksbydate(req.body.book_id, startDate, endDate, 0, email);

  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/getAllBooksuploaded', async (req, res) => {
  const email = (req.body.email);
  const collection_type = req.header('x-current-collection') || 'JYG';
  const books = await BookHelper.getAllBooksuploaded(req.body.book_id, email, collection_type);

  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/getLoginstoday', async (req, res) => {
  const email = req.body.email;
  const startDate = req.body.startDate;
  startDate.setHours(0);
  startDate.setMinutes(0);
  startDate.setSeconds(0);

  const endDate = new Date(req.body.endDate);
  endDate.setDate(endDate.getDate() + 1);
  endDate.setHours(0);
  endDate.setMinutes(0);
  endDate.setSeconds(0);

  let sessions = [];
  const result = await UserSession.aggregate([
    {
      $match: { date: { '$gte': new Date(startDate), '$lte': new Date(endDate) } }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user_id',
        foreignField: '_id',
        as: 'users'
      }
    },
    {
      $group: { '_id': { 'user_id': '$user_id', 'patronid': '$users.patronid' }, count: { $sum: 1 } }
    },
    {
      $group: { '_id': null, 'data': { '$push': { 'patronid': '$_id.patronid', 'user_id': '$_id.user_id', 'count': '$count' } } }
    },
    {
      $project: { data: 1 }
    },
    {
      $sort: { 'data.count': -1 }
    }
  ]);
  if (result && result.length > 0) {
    sessions = [...result[0].data, ...sessions];
  }

  const userdetails = [];
  if (sessions.length > 0) {
    sessions = sessions.sort((a, b) => b.count - a.count);
    const resx = await Admin.findOne({ email: email });
    if (resx) {
      for (const session of sessions) {
        const users_count = session.count;
        userdetails.push({
          patronid: session.patronid[0],
          counts: users_count
        });
      }
    }

    return res.json({
      code: 200,
      data: userdetails,
      message: 'Operation Successful'
    });
  } else {
    return res.json({
      code: 200,
      data: userdetails,
      message: 'Operation Successful'
    });
  }

  /*
	var email = (req.body.email);
	var today = new Date();
	var sessionstoday = [];

	// const usersessions = await UserSession.find({ "timestamp" : { $gte: startOfDay, $lte: endOfDay } });

	// const usersessions2 = await UserSession.aggregate([
		// { $match : { "timestamp" : { $gte: startOfDay, $lte: endOfDay } } },
		// { $group : { "_id": null, count: { $sum: 1 } } },
		// { $project : { count : 1 } }
	// ]);

	console.time('getLoginstoday');
	const usersessions = await UserSession.find({ });
	usersessions.forEach((el) => {
		if ((new Date(el.date).setHours(0, 0, 0, 0)) >= new Date(today).setHours(0, 0, 0, 0) && (new Date(el.date).setHours(0, 0, 0, 0)) <= new Date(today).setHours(0, 0, 0, 0)) {
			sessionstoday.push(el);
		}
	});
	console.timeEnd('getLoginstoday');

	const uniquesession = Array.from(
		new Set(sessionstoday.map((item) => item.user_id))
	);

	const uniqueuserCounts = [];

	var uniqueArray = [];
	for (i = 0; i < uniquesession.length; i++) {
		if (uniqueArray.indexOf(uniquesession[i].toString()) === -1) {
			uniqueArray.push(uniquesession[i].toString());
		}
	}

	for (let user of uniqueArray) {
		let data = {};
		data.user_id = user;
		data.count = 0;
		for (let users of uniquesession) {
			if (users == user) {
				data.count++;
			}
		}
		uniqueuserCounts.push(data);
	}
	const sortedusers = uniqueuserCounts.sort((a, b) => b.count - a.count);

	const userdetails = [];

	for (const sorteduser of sortedusers) {
		await Admin.findOne({
			email: email
		}).then(async (resx) => {
			var nbook = await User.findOne({
				_id: sorteduser.user_id
			});
			if (nbook) {
				let datas = {};
				datas.users = nbook;
				datas.counts = sorteduser.count
				userdetails.push(datas);
			}
		});
	}

	// const userdetails = [];

	return res.json({
		code: 200,
		data: userdetails,
		message: "Operation Successful",
	});
	*/
});
app.post('/getpreviewstoday', async (req, res) => {
  const email = (req.body.email);
  const startDate = new Date();
  startDate.setHours(0);
  startDate.setMinutes(0);
  startDate.setSeconds(0);

  const endDate = new Date();
  endDate.setDate(endDate.getDate() + 1);
  endDate.setHours(0);
  endDate.setMinutes(0);
  endDate.setSeconds(0);

  const books = await BookHelper.previewBooksbydate(req.body.book_id, startDate, endDate, 0, email);
  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

/** 18-01-2022 -> Optimised **/
app.post('/getLoginstodaybydate', async (req, res) => {
  const email = req.body.email;
  const startDate = new Date(req.body.startDate);
  startDate.setHours(0);
  startDate.setMinutes(0);
  startDate.setSeconds(0);

  const endDate = new Date(req.body.endDate);
  endDate.setDate(endDate.getDate() + 1);
  endDate.setHours(0);
  endDate.setMinutes(0);
  endDate.setSeconds(0);

  let sessions = [];
  const result = await UserSession.aggregate([
    {
      $match: { date: { '$gte': new Date(startDate), '$lte': new Date(endDate) } }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user_id',
        foreignField: '_id',
        as: 'users'
      }
    },
    {
      $group: { '_id': { 'user_id': '$user_id', 'patronid': '$users.patronid' }, count: { $sum: 1 } }
    },
    {
      $group: { '_id': null, 'data': { '$push': { 'patronid': '$_id.patronid', 'user_id': '$_id.user_id', 'count': '$count' } } }
    },
    {
      $project: { data: 1 }
    },
    {
      $sort: { 'data.count': -1 }
    }
  ]);
  if (result && result.length > 0) {
    sessions = [...result[0].data, ...sessions];
  }

  const userdetails = [];
  if (sessions.length > 0) {
    sessions = sessions.sort((a, b) => b.count - a.count);
    const resx = await Admin.findOne({ email: email });
    if (resx) {
      for (const session of sessions) {
        const users_count = session.count;
        userdetails.push({
          patronid: session.patronid[0],
          counts: users_count
        });
      }
    }

    return res.json({
      code: 200,
      data: userdetails,
      message: 'Operation Successful'
    });
  } else {
    return res.json({
      code: 200,
      data: userdetails,
      message: 'Operation Successful'
    });
  }
});

app.post('/getRecentBooks', async (req, res) => {
  const collection_type = req.header('x-current-collection');
  let sortedBooks = [];

  const _match = collection_type
    ? {
      'books.collection_type': collection_type,
      $or: [
        { 'books.is_deleted': { $exists: false } },
        { 'books.is_deleted': { $exists: true, $eq: false } }
      ]
    }
    : {
      $or: [
        { 'books.is_deleted': { $exists: false } },
        { 'books.is_deleted': { $exists: true, $eq: false } }
      ]
    };
  const result = await Borrow.aggregate([
    {
      $match: {
        $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
      }
    },
    {
      $lookup: {
        from: 'books',
        localField: 'book_id',
        foreignField: '_id',
        as: 'books'
      }
    },
    {
      $match: _match
    },
    {
      $unwind: '$books'
    },
    {
      $group: {
        '_id': {
          'book_id': '$book_id',
          'books': '$books'
        },
        count: {
          $sum: 1
        }
      }
    },
    {
      $group: {
        '_id': null,
        'data': {
          '$push': {
            'books': {
              'isbn_no': '$_id.books.isbn_no',
              'title': '$_id.books.title',
              'author': '$_id.books.author',
              'cover_photo': '$_id.books.cover_photo',
              'added_by': '$_id.books.added_by'
            },
            'book_id': '$_id.book_id',
            'count': '$count'
          }
        }
      }
    },
    {
      $sort: {
        'data.book_id': -1
      }
    }
  ]);

  const recommendedBooks = [];
  if (result && result.length > 0) {
    sortedBooks = [...result[0].data, ...sortedBooks];
  }
  if (sortedBooks && sortedBooks.length > 0) {
    if (sortedBooks.length > 0) {
      sortedBooks = sortedBooks.sort((a, b) => b.count - a.count);
      sortedBooks = sortedBooks.slice(0, 10);
      for (const sortedBook of sortedBooks) {
        const book_id = sortedBook.book_id;
        const books = sortedBook.books;
        const book_count = sortedBook.count;
        recommendedBooks.push({
          _id: book_id,
          isbn_no: books.isbn_no,
          author: books.author,
          cover_photo: books.cover_photo,
          title: books.title,
          counts: book_count
        });
      }
    }
  }
  return res.json({
    code: 200,
    data: recommendedBooks,
    message: 'Operation Successful'
  });
});

app.post('/getAuthorBooks', async (req, res) => {

  if (!req.body.author) {
    return res.json({
      code: 500,
      data: 'noadata',
      message: 'missing'
    });
  }
  const author = req.body.author;
  const books = await Book.find({
    author: {
      '$regex': regExpReplaceSpecialCharacters(author)
    },
    $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
  }).sort({
    _id: -1
  });
  const count = await Book.find({
    author: {
      '$regex': regExpReplaceSpecialCharacters(author)
    },
    $or: [
      { is_deleted: { $exists: false } },
      { is_deleted: { $exists: true, $eq: false } }
    ]
  }).countDocuments();

  if (count <= 0) return res.json({
    code: 422,
    message: 'No books found',
    books: books
  });
  return res.json({
    code: 200,
    data: books,
    message: 'Operation Successful'
  });
});

app.post('/update', AdminHelper,
  upload.fields([{
    name: 'cover_photo',
    maxCount: 1
  },
  {
    name: 'book_pdf',
    maxCount: 1
  },
  {
    name: 'preview_book',
    maxCount: 1
  }
  ]),
  async (req, res) => {
    try {
      if (!req.body.field || !req.body.value || !req.body.id) {
        res.json({
          code: 422,
          message: 'Missing fields in request',
          success: false
        });
        return res.end();
      }

      if (req.body.field === 'cover_photo' && !req.files.cover_photo) {
        res.json({
          code: 422,
          message: 'Please Upload Cover Image',
          success: false
        });
        return res.end();
      }

      if (req.body.field === 'book_pdf' && !req.files.book_pdf) {
        res.json({
          code: 422,
          message: 'Please Upload a Book File',
          success: false
        });
        return res.end();
      }

      if (req.body.field === 'preview_book' && !req.files.preview_book) {
        res.json({
          code: 422,
          message: 'Please Upload a Preview File',
          success: false
        });
        return res.end();
      }

      const field = req.body.field;
      const value = req.body.value;

      if (field === 'cover_photo' && req.files.cover_photo) {
        const cover_photoFile = req.files.cover_photo[0];
        const book = await Book.findOne({
          _id: req.body.id
        }).catch((err) => {
          res.json({
            code: 500,
            message: 'Internal error at finding book',
            success: false
          });
        });
        if (!book) {
          return res.json({
            code: 422,
            message: 'No book exist.',
            success: false
          });
        }

        await Book.findOneAndUpdate({
          _id: req.body.id
        }, {
          cover_photo: cover_photoFile.path
        }, {
          useFindAndModify: false
        },
        (err, updated) => {
          if (err)
            return res.json({
              code: 500,
              message: 'Internal error at updating book-cover',
              success: false
            });
          return res.json({
            code: 200,
            data: updated,
            message: 'Operation successful'
          });
        }
        );
      } else if (field === 'book_pdf' && req.files.book_pdf) {
        const book_pdfFile = req.files.book_pdf[0];
        const filename = book_pdfFile.filename;
        const isbn_no = req.body.isbn_no;
        const path = book_pdfFile.path;

        if (book_pdfFile.mimetype === 'application/pdf') {
          if (filename.includes('preview') === false) {
            await PDFNet.initialize();
            const filename_upload = process.env.UPLOAD_path + filename;
            const doc = await PDFNet.PDFDoc.createFromURL(filename_upload);

            const i = await doc.getPageIterator();
            let counts = 0;
            for (i; await i.hasNext(); i.next()) {
              counts++;
            }
            if (counts > 30) {
              doc.initSecurityHandler();
              let pageNum = await doc.getPageCount();
              while (pageNum > 30) {
                const itr = await doc.getPageIterator(pageNum);
                doc.pageRemove(itr);
                pageNum -= 1;
              }
            }
            let file_preview_name = '';
            file_preview_name = filename.replace('.pdf', '_preview.pdf');
            await doc.save('uploads/' + file_preview_name, PDFNet.SDFDoc.SaveOptions.e_linearized);
            PDFNet.shutdown();
          }
        } else if (book_pdfFile.mimetype === 'application/epub+zip') {
          if (fs.existsSync(process.env.EPUB_VIEWER_PATH + filename)) {
            fs.unlink(process.env.EPUB_VIEWER_PATH + filename, (err) => {
              console.log(err);
              if (err) {
                return res.json({
                  code: 500,
                  message: 'Internal error at updating Preview book',
                  success: false
                });
              }
            });
          }

          /* After uploading the epub file, move it to its desired location to load in the browser
					var oldPath = 'uploads/'+filename;
					var newPath = process.env.EPUB_VIEWER_PATH+filename;
					fs.rename( oldPath, newPath, function (err) {
						if (err){
							console.log(err);
						}else{
							path = '/bibi/?book=__samples/'+filename;
							console.log('Successfully moved!');
						}
					}); */
        }

        const book = await Book.findOne({
          _id: req.body.id
        }).catch((err) => {
          res.json({
            code: 500,
            message: 'Internal error at finding book',
            success: false
          });
        });
        if (!book) {
          return res.json({
            code: 422,
            message: 'No book exist.',
            success: false
          });
        }
        await Book.findOneAndUpdate({
          _id: req.body.id
        }, {
          book_pdf: path
          // preview_book: 'uploads/' + file_preview_name
        }, {
          useFindAndModify: false
        },
        (err, updated) => {

          /*
						const filePath = req.files.book_pdf[0].path;
						var ext = path.extname(filename);
						if (ext == ".epub") {
							var baseFileName = path.basename(filename, ext);
							const options = {
								input: filePath, //path to epub
								output: 'uploads/' + baseFileName + '.pdf', //path to pdf
							};
							convert(options, function(err) {
								if (err) console.log(err);
							});
						}*/

          if (err) {
            return res.json({
              code: 500,
              message: 'Internal error at updating book-cover',
              success: false
            });
          }

          return res.json({
            code: 200,
            data: updated,
            message: 'Operation successful'
          });
        }
        );
      } else if (field === 'preview_book' && req.files.preview_book) {
        const filename = req.files.preview_book[0].filename;
        const path = req.files.preview_book[0].path;
        const book = await Book.findOne({
          _id: req.body.id
        }).catch((err) => {
          res.json({
            code: 500,
            message: 'Internal error at finding book',
            success: false
          });
        });

        if (!book) {
          return res.json({
            code: 422,
            message: 'No book exist.',
            success: false
          });
        }

        // if (req.files.preview_book[0].mimetype == 'application/pdf') {
        // if (fs.existsSync(book.preview_book)) {
        // fs.unlink(book.preview_book, (err) => {
        // if (err) {
        // return res.json({
        // code: 500,
        // message: "Internal error at updating Preview book",
        // success: false,
        // });
        // }
        // });
        // }
        // }

        if (req.files.preview_book[0].mimetype === 'application/epub+zip') {
          // path = '/bibi/?book=__samples/'+filename;
          if (fs.existsSync(process.env.EPUB_VIEWER_PATH + filename)) {
            fs.unlink(process.env.EPUB_VIEWER_PATH + filename, (err) => {
              console.log(err);
              if (err) {
                return res.json({
                  code: 500,
                  message: 'Internal error at updating Preview book',
                  success: false
                });
              }
            });
          }

          /* After uploading the epub file, move it to its desired location to load in the browser
					var oldPath = 'uploads/'+filename;
					var newPath = process.env.EPUB_VIEWER_PATH+filename;
					fs.rename( oldPath, newPath, function (err) {
						if (err){
							console.log(err);
						}else{
							console.log('Successfully moved!');
						}
					}); */
        }
        await Book.findOneAndUpdate({
          _id: req.body.id
        }, {
          preview_book: path.indexOf('uploads') !== -1 ? path : 'uploads/' + path
        }, {
          useFindAndModify: false
        },
        (err, updated) => {
          if (err)
            return res.json({
              code: 500,
              message: 'Internal error at updating Preview book',
              success: false
            });

          return res.json({
            code: 200,
            data: updated,
            message: 'Operation successful'
          });
        }
        );
      } else if (field === 'category_id') {
        const arr = req.body.value.split(',');
        await Book.findOneAndUpdate({
          _id: req.body.id
        }, {
          $set: {
            category_id: arr
          }
        }, {
          useFindAndModify: false
        },
        (err, updated) => {
          if (err)
            return res.json({
              code: 500,
              message: 'Internal error at updating category_id',
              success: false
            });
          return res.json({
            code: 200,
            data: updated,
            message: 'Operation successful'
          });
        }
        );
      } else if (field === 'Recommended') {
        const arr = req.body.value;
        let book_recomm_datetime = null;
        if (arr === 'true') {
          book_recomm_datetime = new Date();
        }
        await Book.findOneAndUpdate({
          _id: req.body.id
        }, {
          $set: {
            book_recomm: arr,
            book_recomm_datetime: book_recomm_datetime
          }
        }, {
          useFindAndModify: false
        },
        (err, updated) => {
          if (err)
            return res.json({
              code: 500,
              message: 'Internal error at updating Recommended Field',
              success: false
            });
          return res.json({
            code: 200,
            data: updated,
            message: 'Operation successful'
          });
        }
        );
      } else {
        if (req.body.field === 'stock_quantity') {
          // 使用 BookHelper 统一处理库存更新
          const result = await BookHelper.updateStockQuantity(req.body.id, parseInt(req.body.value));

          if (!result.success) {
            return res.json({
              code: 500,
              message: result.message,
              success: false
            });
          }

          console.log('Stock quantity update successful:', result.data);
        }

        await Book.findByIdAndUpdate(
          req.body.id, {
            [req.body.field]: req.body.value
          }, {
            useFindAndModify: false
          },
          (err, updated) => {
            if (err)
              return res.json({
                code: 500,
                message: 'Internal error.',
                success: false
              });
            return res.json({
              code: 200,
              data: updated,
              message: 'Operation successful'
            });
          }
        );

      }
    } catch (e) {
      console.log(e.message);
      res.json({
        code: 422,
        message: e.message,
        success: false
      });
      return res.end();
    }
  }
);

app.post('/reserveDetailsReport', async (req, res) => {
  const collection_type = req.header('x-current-collection');

  console.log(collection_type);

  const condition = [];
  let reserveMatchCondition = {
    is_deleted: false
  };
  if (req.body.endDate || req.body.startDate) {
    if (req.body.startDate && req.body.endDate) {
      reserveMatchCondition = {
        ...reserveMatchCondition,
        reserve_date: {
          $gte: new Date(Number(req.body.startDate) * 1000),
          $lte: new Date(Number(req.body.endDate) * 1000)
        }
      };
    }
    if (req.body.startDate && !req.body.endDate) {
      reserveMatchCondition = {
        ...reserveMatchCondition,
        reserve_date: {
          $gte: new Date(Number(req.body.startDate) * 1000)
        }
      };
    }
    if (!req.body.startDate && req.body.endDate) {
      reserveMatchCondition = {
        ...reserveMatchCondition,
        reserve_date: {
          $lte: new Date(Number(req.body.endDate) * 1000)
        }
      };
    }
  }

  condition.push({
    $match: reserveMatchCondition
  });

  condition.push({
    $group: {
      _id: {
        book_id: '$book_id',
        reserve_date: {
          $dateToString: {
            format: '%d/%m/%Y',
            date: '$reserve_date'
          }
        }
      },
      count: {
        $sum: 1
      }
    }
  }, {
    $project: {
      book_id: '$_id.book_id',
      reserve_date: '$_id.reserve_date',
      count: '$count',
      _id: 0
    }
  },
  {
    $sort: {
      reserve_date: -1
    }
  },
  {
    $lookup: {
      from: 'books',
      localField: 'book_id',
      foreignField: '_id',
      as: 'book'
    }
  },
  {
    $unwind: '$book'
  },
  {
    $match: {
      $or: [
        { 'book.is_deleted': { $exists: false } },
        { 'book.is_deleted': { $exists: true, $eq: false } }
      ]
    }
  },
  {
    $project: {
      reserveDate: '$reserve_date',
      isbn_no: '$book.isbn_no',
      title: '$book.title',
      publishingGroup: '$book.publishingGroup',
      imprints: '$book.imprints',
      collectionType: '$book.collection_type',
      author: '$book.author',
      reserves: '$count',
      category_id: '$book.category_id'
    }
  });

  if (collection_type) {
    condition.push({
      $match: {
        collectionType: collection_type
      }
    });
  }

  condition.push({
    $lookup: {
      from: 'categories',
      localField: 'category_id',
      foreignField: '_id',
      as: 'categories'
    }
  });

  let items = await Reserve.aggregate(condition);

  const otherBooks = await Book.aggregate([
    {
      $match: {
        $and: [
          {
            $or: [
              { is_deleted: { $exists: false } },
              { is_deleted: { $exists: true, $eq: false } }
            ]
          },
          {
            collection_type,
            isbn_no: {
              $nin: (items || []).map(x => x.isbn_no).filter(x => !!x)
            }
          }
        ]
      }
    },
    {
      $project: {
        // reserveDate: false,
        isbn_no: '$isbn_no',
        title: '$title',
        publishingGroup: '$publishingGroup',
        imprints: '$imprints',
        collectionType: '$collection_type',
        author: '$author',
        reserves: '0',
        category_id: '$category_id'

      }
    },
    {
      $lookup: {
        from: 'categories',
        localField: 'category_id',
        foreignField: '_id',
        as: 'categories'
      }
    }
  ]);


  items = items.concat(otherBooks);

  return res.json({
    code: 200,
    data: (items || []).map(x => ({
      asOfReservedDate: req.body.endDate ? moment(Number(req.body.endDate) * 1000).tz('Asia/Hong_Kong').format('D/M/YYYY') : moment().tz('Asia/Hong_Kong').format('D/M/YYYY'),
      isbnNumber: x.isbn_no,
      bookName: x.title,
      authorName: x.author,
      category: (x.categories || []).map(y => y.name).join(','),
      reservedCopies: x.reserves,
      reservedDate: x.reserveDate,
      publishGroup: x.publishingGroup,
      imprints: x.imprints

    })),
    message: 'Operation Successful'
  });

});

app.post('/reserveSummaryReport', async (req, res) => {
  const collection_type = req.header('x-current-collection');

  const condition = [];
  let reserveMatchCondition = {
    is_deleted: false
  };
  if (req.body.endDate || req.body.startDate) {
    if (req.body.startDate && req.body.endDate) {
      reserveMatchCondition = {
        ...reserveMatchCondition,
        reserve_date: {
          $gte: new Date(Number(req.body.startDate) * 1000),
          $lte: new Date(Number(req.body.endDate) * 1000)
        }
      };
    }
    if (req.body.startDate && !req.body.endDate) {
      reserveMatchCondition = {
        ...reserveMatchCondition,
        reserve_date: {
          $gte: new Date(Number(req.body.startDate) * 1000)
        }
      };
    }
    if (!req.body.startDate && req.body.endDate) {
      reserveMatchCondition = {
        ...reserveMatchCondition,
        reserve_date: {
          $lte: new Date(Number(req.body.endDate) * 1000)
        }
      };
    }
  }

  condition.push({
    $match: reserveMatchCondition
  });

  condition.push({
    $group: {
      _id: {
        book_id: '$book_id',
        reserve_date: {
          $dateToString: {
            format: '%d/%m/%Y',
            date: '$reserve_date'
          }
        }
      },
      count: {
        $sum: 1
      }
    }
  }, {
    $project: {
      book_id: '$_id.book_id',
      reserve_date: '$_id.reserve_date',
      count: '$count',
      _id: 0
    }
  },
  {
    $sort: {
      reserve_date: -1
    }
  },
  {
    $lookup: {
      from: 'books',
      localField: 'book_id',
      foreignField: '_id',
      as: 'book'
    }
  },
  {
    $unwind: '$book'
  },
  {
    $match: {
      $or: [
        { 'book.is_deleted': { $exists: false } },
        { 'book.is_deleted': { $exists: true, $eq: false } }
      ]
    }
  },
  {
    $project: {
      reserveDate: '$reserve_date',
      isbn_no: '$book.isbn_no',
      title: '$book.title',
      publishingGroup: '$book.publishingGroup',
      imprints: '$book.imprints',
      collectionType: '$book.collection_type',
      author: '$book.author',
      reserves: '$count',
      category_id: '$book.category_id'
    }
  });

  if (collection_type) {
    condition.push({
      $match: {
        collectionType: collection_type
      }
    });
  }

  condition.push({
    $lookup: {
      from: 'categories',
      localField: 'category_id',
      foreignField: '_id',
      as: 'categories'
    }
  });

  const items = await Reserve.aggregate(condition);

  return res.json({
    code: 200,
    data: {
      reservedCopies: _.sum((items || []).map(x => x.reserves)),
      reservedBooks: Array.from(new Set((items || []).map(x => x.isbn_no))).length
    },
    message: 'Operation Successful'
  });

});

const { generateBorrowDetailsReport } = require('../src/service/book/borrowDetailsReport');

app.post('/borrowDetailsReport', async (req, res) => {
  const collection_type = req.header('x-current-collection');
  const { startDate, endDate } = req.body;

  if (!startDate || !endDate) {
    return res.json({
      code: 422,
      message: 'Missing required fields: startDate, endDate',
      status: false
    });
  }

  // try {
  const finalResult = await generateBorrowDetailsReport(startDate, endDate, collection_type);

  return res.json({
    code: 200,
    data: finalResult,
    message: 'Operation Successful'
  });
  // } catch (error) {
  // 	console.error('Error in borrowDetailsReport:', error);
  // 	return res.json({
  // 		code: 500,
  // 		message: "Internal server error",
  // 		status: false
  // 	});
  // }
});

app.post('/borrowSummaryReport', async (req, res) => {
  const collection_type = req.header('x-current-collection');

  if (!req.body.startDate || !req.body.endDate) {
    return res.json({
      code: 422,
      message: 'Missing required fields: startDate, endDate',
      status: false
    });
  }

  try {
    const result = await generateBorrowSummaryReport(req.body.startDate, req.body.endDate, collection_type);
    return res.json({
      code: 200,
      data: result,
      message: 'Operation Successful'
    });
  } catch (error) {
    console.error('Error in borrowSummaryReport:', error);
    return res.json({
      code: 500,
      message: 'Internal server error',
      status: false
    });
  }
});

module.exports = {
  router: app
};
