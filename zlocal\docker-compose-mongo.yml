version: "3.4"

services:
  mongo:
    image: mongo:4.4.0
    restart: always
    ports:
      - 27017:27017
    networks:
      - traefik-net
    environment:
      MONGO_INITDB_ROOT_USERNAME: general-user
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: ebook
    volumes:
      - /home/<USER>/docker/mongo/data:/data/db
      - ./docker-entrypoint-initdb/mongo.init.js:/docker-entrypoint-initdb.d/mongo.init.js:ro

networks:
  traefik-net:
    external: true
