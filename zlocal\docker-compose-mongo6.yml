version: "3.8"
services:
  mongo6:
    image: mongo:6.0
    container_name: mongo6-test-instance
    restart: unless-stopped  # 修改重启策略
    ports:
      - 27017:27017
    environment:
      MONGO_INITDB_ROOT_USERNAME: general-user
      MONGO_INITDB_ROOT_PASSWORD: password
    volumes:
      - ./mongo6-data:/data/db
      - ./mongo6-keyfile:/etc/mongo-keyfile
    command: [
      "--replSet", "rs0",
      "--keyFile", "/etc/mongo-keyfile",
      "--bind_ip_all",
      "--auth"  # 显式启用认证
    ]
    networks: # 关键修复：显式声明网络
      - traefik-net

networks:
  traefik-net:
    external: true