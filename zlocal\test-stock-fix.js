const mongoose = require('mongoose');
const Book = require('../Model/Book');
const Borrow = require('../Model/Borrow');
const Reserve = require('../Model/Reserve');
const User = require('../Model/User');
const BookHelper = require('../Helper/BookHelper');
const { syncReturnBooks } = require('../src/action/bookActions');

// 加载环境变量
require('dotenv').config();

// 连接数据库
mongoose.connect(process.env.DB_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function testStockConsistency() {
  console.log('=== 测试库存一致性修复 ===');

  try {
    // 查找一本有库存的书
    const book = await Book.findOne({
      available_quantity: { $gt: 0 },
      stock_quantity: { $gt: 0 }
    });

    if (!book) {
      console.log('没有找到有库存的书籍');
      return;
    }

    console.log(`测试书籍: ${book.title}`);
    console.log(`当前库存: ${book.available_quantity}/${book.stock_quantity}`);

    // 创建一些测试预约
    const testUsers = await User.find({ email: { $ne: '' } }).limit(5);
    if (testUsers.length === 0) {
      console.log('没有找到有效的测试用户');
      return;
    }

    console.log(`找到 ${testUsers.length} 个测试用户`);

    // 清理之前的测试预约
    await Reserve.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });

    // 创建超过库存数量的预约
    const reservations = [];
    for (let i = 0; i < book.available_quantity + 2; i++) {
      const user = testUsers[i % testUsers.length];
      const reserve = new Reserve({
        user_id: user._id,
        book_id: book._id,
        email: user.email,
        reserve_date: new Date(),
        status: true,
        is_mailed: false,
        is_deleted: false,
        is_blocked: false
      });
      await reserve.save();
      reservations.push(reserve);
      console.log(`创建预约 ${i + 1}: 用户 ${user._id}`);
    }

    console.log(`\n创建了 ${reservations.length} 个预约，但只有 ${book.available_quantity} 个库存`);

    // 测试 syncReturnBooks 函数
    console.log('\n=== 开始测试 syncReturnBooks ===');
    const session = await mongoose.startSession();

    try {
      await session.withTransaction(async () => {
        await syncReturnBooks(book._id.toString(), { session });
      });
    } catch (error) {
      console.log('事务执行完成，可能有部分预约因库存不足而失败');
    }

    // 检查结果
    const updatedBook = await Book.findById(book._id);
    const borrowRecords = await Borrow.find({
      book_id: book._id,
      returned: false
    });
    const remainingReserves = await Reserve.find({
      book_id: book._id,
      is_deleted: false
    });

    console.log('\n=== 测试结果 ===');
    console.log(`书籍库存: ${updatedBook.available_quantity}/${updatedBook.stock_quantity}`);
    console.log(`借阅记录数: ${borrowRecords.length}`);
    console.log(`剩余预约数: ${remainingReserves.length}`);

    // 验证数据一致性
    const expectedAvailable = book.stock_quantity - borrowRecords.length;
    if (updatedBook.available_quantity === expectedAvailable) {
      console.log('✅ 库存数据一致性检查通过');
    } else {
      console.log(`❌ 库存数据不一致: 期望 ${expectedAvailable}, 实际 ${updatedBook.available_quantity}`);
    }

    // 清理测试数据
    await Borrow.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });
    await Reserve.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });

    // 恢复原始库存
    await Book.findByIdAndUpdate(book._id, {
      available_quantity: book.stock_quantity
    });

    console.log('测试数据已清理');

  } catch (error) {
    console.error('测试过程中出错:', error);
  } finally {
    await mongoose.connection.close();
  }
}

// 运行测试
testStockConsistency();
