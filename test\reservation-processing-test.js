/**
 * 预约处理"跳跃式"问题测试用例
 * 
 * 测试场景：3个阅读人归还，4个预约用户等待
 * 预期结果：预约应该按顺序处理，不再出现"跳跃式"现象
 * 
 * 数据库连接：***********************************************************
 */

const mongoose = require('mongoose');
const User = require('../Model/User');
const Book = require('../Model/Book');
const Reserve = require('../Model/Reserve');
const Reading = require('../Model/Reading');
const Borrow = require('../Model/Borrow');
const BookHelper = require('../Helper/BookHelper');
const { syncReturnBooks } = require('../src/action/bookActions');

// 数据库连接配置
const DB_URL = '***********************************************************';

// 测试数据配置
const TEST_CONFIG = {
  // 测试书籍ID（需要在数据库中存在）
  BOOK_ID: null, // 将在测试中动态获取
  
  // 测试用户（需要在数据库中存在）
  READING_USERS: [], // 3个阅读用户
  RESERVE_USERS: [], // 4个预约用户
  
  // 测试客户端ID（用于Reading操作）
  CLIENT_IDS: ['test-client-1', 'test-client-2', 'test-client-3']
};

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    await mongoose.connect(DB_URL, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useCreateIndex: true,
      useFindAndModify: false
    });
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDB() {
  await mongoose.disconnect();
  console.log('✅ 数据库连接已断开');
}

/**
 * 获取测试数据
 */
async function getTestData() {
  console.log('\n📋 获取测试数据...');
  
  // 1. 获取一本有库存的书籍
  const book = await Book.findOne({
    is_deleted: false,
    available_quantity: { $gte: 1 }
  });
  
  if (!book) {
    throw new Error('没有找到可用的测试书籍');
  }
  
  TEST_CONFIG.BOOK_ID = book._id.toString();
  console.log(`📚 测试书籍: ${book.title} (ID: ${TEST_CONFIG.BOOK_ID})`);
  console.log(`📦 当前库存: ${book.available_quantity}/${book.stock_quantity}`);
  
  // 2. 获取测试用户（至少需要7个用户：3个阅读 + 4个预约）
  const users = await User.find({
    is_active: true
  }).limit(10);
  
  if (users.length < 7) {
    throw new Error(`需要至少7个用户进行测试，当前只有${users.length}个`);
  }
  
  TEST_CONFIG.READING_USERS = users.slice(0, 3);
  TEST_CONFIG.RESERVE_USERS = users.slice(3, 7);
  
  console.log('👥 阅读用户:');
  TEST_CONFIG.READING_USERS.forEach((user, index) => {
    console.log(`  ${index + 1}. ${user.name || user.patronid} (${user._id})`);
  });
  
  console.log('👥 预约用户:');
  TEST_CONFIG.RESERVE_USERS.forEach((user, index) => {
    console.log(`  ${index + 1}. ${user.name || user.patronid} (${user._id})`);
  });
}

/**
 * 清理测试数据
 */
async function cleanupTestData() {
  console.log('\n🧹 清理测试数据...');
  
  const bookId = TEST_CONFIG.BOOK_ID;
  
  // 清理阅读记录
  await Reading.deleteMany({
    book_id: mongoose.Types.ObjectId(bookId),
    client_id: { $in: TEST_CONFIG.CLIENT_IDS }
  });
  
  // 清理预约记录
  const userIds = TEST_CONFIG.RESERVE_USERS.map(user => user._id);
  await Reserve.deleteMany({
    book_id: mongoose.Types.ObjectId(bookId),
    user_id: { $in: userIds }
  });
  
  // 清理借阅记录
  await Borrow.deleteMany({
    book_id: mongoose.Types.ObjectId(bookId),
    user_id: { $in: userIds }
  });
  
  console.log('✅ 测试数据清理完成');
}

/**
 * 设置测试环境
 */
async function setupTestEnvironment() {
  console.log('\n🔧 设置测试环境...');
  
  const bookId = TEST_CONFIG.BOOK_ID;
  
  // 1. 确保书籍有足够库存
  await Book.findByIdAndUpdate(bookId, {
    $set: {
      available_quantity: 3,
      stock_quantity: 3
    }
  });
  
  // 2. 创建3个活跃的阅读记录
  const readingPromises = TEST_CONFIG.READING_USERS.map((user, index) => {
    return new Reading({
      book_id: mongoose.Types.ObjectId(bookId),
      client_id: TEST_CONFIG.CLIENT_IDS[index],
      ip: '127.0.0.1',
      read_date: new Date(),
      return_date: new Date(),
      returned: false
    }).save();
  });
  
  await Promise.all(readingPromises);
  console.log('✅ 创建了3个活跃的阅读记录');
  
  // 3. 创建4个预约记录
  const reservePromises = TEST_CONFIG.RESERVE_USERS.map((user, index) => {
    return new Reserve({
      user_id: user._id,
      book_id: mongoose.Types.ObjectId(bookId),
      email: user.email || `test${index}@example.com`,
      reserve_date: new Date(Date.now() + index * 1000), // 确保预约时间不同
      status: true,
      is_deleted: false,
      is_mailed: false
    }).save();
  });
  
  await Promise.all(reservePromises);
  console.log('✅ 创建了4个预约记录');
  
  // 4. 减少库存到0（模拟书籍被借完）
  await Book.findByIdAndUpdate(bookId, {
    $set: { available_quantity: 0 }
  });
  
  console.log('✅ 测试环境设置完成');
}

/**
 * 模拟阅读归还操作
 */
async function simulateReadingReturn(clientId, returnIndex) {
  console.log(`\n📖 模拟第${returnIndex}次阅读归还 (client_id: ${clientId})...`);
  
  const bookId = TEST_CONFIG.BOOK_ID;
  
  // 1. 查找阅读记录
  const reading = await Reading.findOne({
    book_id: mongoose.Types.ObjectId(bookId),
    client_id: clientId,
    returned: false
  });
  
  if (!reading) {
    console.log(`❌ 没有找到活跃的阅读记录: ${clientId}`);
    return;
  }
  
  // 2. 标记为已归还
  await Reading.findByIdAndUpdate(reading._id, {
    $set: { returned: true }
  });
  
  // 3. 增加库存
  const bookBefore = await Book.findById(bookId);
  await BookHelper.updateAvailableStock(bookId, 'return');
  const bookAfter = await Book.findById(bookId);
  
  console.log(`📦 库存变化: ${bookBefore.available_quantity} -> ${bookAfter.available_quantity}`);
  
  // 4. 处理预约
  console.log('🔄 开始处理预约...');
  await syncReturnBooks(bookId);
  
  // 5. 检查预约处理结果
  const processedReserves = await Reserve.find({
    book_id: mongoose.Types.ObjectId(bookId),
    is_deleted: true
  }).populate('user_id');
  
  const newBorrows = await Borrow.find({
    book_id: mongoose.Types.ObjectId(bookId),
    returned: false
  }).populate('user_id');
  
  console.log(`📋 已处理的预约数量: ${processedReserves.length}`);
  console.log(`📚 新创建的借阅记录: ${newBorrows.length}`);
  
  if (newBorrows.length > 0) {
    console.log('👤 新借阅用户:');
    newBorrows.forEach((borrow, index) => {
      console.log(`  ${index + 1}. ${borrow.user_id.name || borrow.user_id.patronid} (${borrow.user_id._id})`);
    });
  }
  
  // 6. 检查最终库存
  const finalBook = await Book.findById(bookId);
  console.log(`📦 最终库存: ${finalBook.available_quantity}`);
  
  return {
    processedReserves: processedReserves.length,
    newBorrows: newBorrows.length,
    finalStock: finalBook.available_quantity
  };
}

/**
 * 运行完整测试
 */
async function runFullTest() {
  console.log('🚀 开始预约处理测试...');
  console.log('=' * 60);
  
  try {
    // 连接数据库
    await connectDB();
    
    // 获取测试数据
    await getTestData();
    
    // 清理旧的测试数据
    await cleanupTestData();
    
    // 设置测试环境
    await setupTestEnvironment();
    
    console.log('\n🎯 开始模拟3次阅读归还...');
    console.log('预期结果：每次归还应该触发1个预约，按顺序处理');
    
    const results = [];
    
    // 模拟3次阅读归还
    for (let i = 0; i < 3; i++) {
      const result = await simulateReadingReturn(TEST_CONFIG.CLIENT_IDS[i], i + 1);
      results.push(result);
      
      // 等待一秒，确保操作完成
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 输出测试结果
    console.log('\n📊 测试结果总结:');
    console.log('=' * 40);
    
    let totalProcessed = 0;
    let totalBorrows = 0;
    
    results.forEach((result, index) => {
      console.log(`第${index + 1}次归还:`);
      console.log(`  - 处理的预约: ${result.processedReserves}`);
      console.log(`  - 新借阅记录: ${result.newBorrows}`);
      console.log(`  - 剩余库存: ${result.finalStock}`);
      
      totalProcessed += result.processedReserves;
      totalBorrows += result.newBorrows;
    });
    
    console.log(`\n总计:`);
    console.log(`  - 总处理预约: ${totalProcessed}/4`);
    console.log(`  - 总借阅记录: ${totalBorrows}`);
    
    // 验证结果
    if (totalProcessed === 3 && totalBorrows === 3) {
      console.log('\n✅ 测试通过！预约处理正常，按顺序处理');
    } else if (totalProcessed < 3) {
      console.log('\n⚠️  测试异常：预约处理数量不足');
    } else {
      console.log('\n❌ 测试失败：预约处理出现问题');
    }
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error);
  } finally {
    // 清理测试数据
    await cleanupTestData();
    
    // 断开数据库连接
    await disconnectDB();
  }
}

/**
 * 快速验证当前数据库状态
 */
async function quickCheck() {
  console.log('🔍 快速检查数据库状态...');
  
  try {
    await connectDB();
    
    // 检查书籍数量
    const bookCount = await Book.countDocuments({ is_deleted: false });
    console.log(`📚 书籍总数: ${bookCount}`);
    
    // 检查用户数量
    const userCount = await User.countDocuments({ is_active: true });
    console.log(`👥 活跃用户数: ${userCount}`);
    
    // 检查预约数量
    const reserveCount = await Reserve.countDocuments({ is_deleted: false });
    console.log(`📋 活跃预约数: ${reserveCount}`);
    
    // 检查借阅数量
    const borrowCount = await Borrow.countDocuments({ returned: false });
    console.log(`📖 活跃借阅数: ${borrowCount}`);
    
    // 检查阅读数量
    const readingCount = await Reading.countDocuments({ returned: false });
    console.log(`📱 活跃阅读数: ${readingCount}`);
    
    console.log('✅ 数据库状态检查完成');
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await disconnectDB();
  }
}

// 导出测试函数
module.exports = {
  runFullTest,
  quickCheck,
  connectDB,
  disconnectDB,
  getTestData,
  cleanupTestData,
  setupTestEnvironment,
  simulateReadingReturn
};

// 如果直接运行此文件，执行完整测试
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--check')) {
    quickCheck();
  } else {
    runFullTest();
  }
}
