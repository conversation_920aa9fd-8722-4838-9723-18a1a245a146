const app = require('express').Router();
const mongoose = require('mongoose');
const multer = require('multer');

const Reading = require('../Model/Reading');
const ReadingRecord = require('../Model/ReadingRecord');
const ReadingCount = require('../Model/ReadingCount');

const BookHelper = require('../Helper/BookHelper');
const { batchReturnReadingBooks } = require('../src/action/readAction');
const { syncReturnBooks } = require('../src/action/bookActions');

const upload = multer();

// ===== 缓存管理工具 =====
const cache = {};

/**
 * Reading 缓存管理工具类
 * 专门用于 Reading 操作的缓存管理
 */
class ReadingCacheManager {
  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {string} operation - 操作类型
   * @param {number} expireMinutes - 过期时间（分钟）
   */
  static set(key, operation, expireMinutes = 5) {
    const expireTime = Date.now() + expireMinutes * 60 * 1000;
    cache[key] = {
      timestamp: Date.now(),
      expireTime: expireTime,
      operation: operation
    };

    // 设置自动清理定时器（兜底保障）
    setTimeout(() => {
      if (cache[key] && Date.now() >= cache[key].expireTime) {
        delete cache[key];
        console.log(`Reading cache auto-expired: ${key}`);
      }
    }, expireMinutes * 60 * 1000);

    console.log(`Reading cache set: ${key} (expires in ${expireMinutes} minutes)`);
  }

  /**
   * 检查缓存是否存在
   * @param {string} key - 缓存键
   * @returns {boolean} 是否存在
   */
  static exists(key) {
    if (!cache[key]) return false;

    // 检查是否过期
    if (Date.now() >= cache[key].expireTime) {
      delete cache[key];
      console.log(`Reading cache expired and removed: ${key}`);
      return false;
    }

    return true;
  }

  /**
   * 清除缓存
   * @param {string} key - 缓存键
   * @param {string} reason - 清除原因
   */
  static clear(key, reason = 'normal') {
    if (cache[key]) {
      delete cache[key];
      console.log(`Reading cache cleared (${reason}): ${key}`);
    }
  }

  /**
   * 清理所有过期缓存
   */
  static cleanExpired() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const key in cache) {
      if (cache[key].expireTime <= now) {
        delete cache[key];
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`Cleaned ${cleanedCount} expired reading cache entries`);
    }
  }
}

// 定期清理过期缓存（每3分钟执行一次）
setInterval(() => {
  ReadingCacheManager.cleanExpired();
}, 3 * 60 * 1000);

app.use('/*', (req, res, next) => next());

app.post('/start', upload.none(), async (req, res) => {
  // ===== 参数验证 =====
  if (!req.body.book_id || !req.body.client_id) {
    return res.json({
      code: 418,
      message: 'Missing required fields',
      status: false
    });
  }

  const ip = req.header['cf-connecting-ip'] || req.ip;
  const countryCode = req.header('CF-IPCountry') || 'Unknown';
  const book_id = req.body.book_id;
  const client_id = req.body.client_id;

  // ===== 地区验证 =====
  if (countryCode != 'HK') {
    return res.json({
      code: 401,
      message: 'must be login',
      status: false
    });
  }

  // ===== 防重复提交机制 =====
  // Reading 开始基于 client_id + book_id，防止同一客户端重复开始阅读同一本书
  const cacheKey = 'reading_start_' + client_id + '_' + book_id;

  // 检查是否正在处理中
  if (ReadingCacheManager.exists(cacheKey)) {
    return res.json({
      code: 418,
      message: 'Reading start processing in progress',
      status: false
    });
  }

  // 设置缓存标记，3分钟过期（Reading 开始操作相对简单）
  ReadingCacheManager.set(cacheKey, 'reading_start', 3);

  // ===== 事務前預檢查：使用 isBookInStock() 做初步庫存檢查 =====
  const preCheckedQuantity = await BookHelper.isBookInStock(book_id);
  if (preCheckedQuantity <= 0) {
    ReadingCacheManager.clear(cacheKey, 'book out of stock');
    return res.json({
      code: 401,
      message: 'Book not has stock',
      status: false
    });
  }

  // 创建数据库会话用于事务
  const session = await mongoose.startSession();

  try {
    // ===== 使用事务确保数据一致性 =====
    let businessResult = null;

    await session.withTransaction(async () => {
      // ===== 第一步：检查是否已有活跃的阅读记录 =====
      const existingReading = await Reading.findOne({
        client_id: client_id,
        book_id: book_id,
        returned: false
      }).session(session);

      if (existingReading) {
        console.log(`Client ${client_id} already has active reading for book ${book_id}, reading_id: ${existingReading._id}`);
        ReadingCacheManager.clear(cacheKey, 'existing reading found');
        businessResult = {
          code: 200,
          message: 'Book start reading',
          data: {
            reading_id: existingReading._id
          }
        };
        return; // 事务成功，但不返回业务数据
      }

      // ===== 第二步：减少库存（原子操作） =====
      // 事務內部：只使用原子操作，不再調用 isBookInStock()
      await BookHelper.updateAvailableStock(book_id, 'reading', {
        session,
        preCheckedQuantity
      });

      // ===== 第四步：创建阅读记录 =====
      const newReading = new Reading({
        book_id: book_id,
        client_id: client_id,
        ip: ip,
        read_date: new Date(),
        return_date: new Date(),
        returned: false
      });
      await newReading.save({ session });

      // ===== 第五步：创建阅读记录详情 =====
      const newReadingRecord = new ReadingRecord({
        reading_id: newReading._id,
        read_time: new Date()
      });
      await newReadingRecord.save({ session });

      console.log(`New reading started - Client: ${client_id}, Book: ${book_id}, Reading ID: ${newReading._id}, IP: ${ip}`);
      console.log('Reading record ID:', newReadingRecord._id);

      // 设置业务结果，但不从事务中返回
      businessResult = {
        code: 200,
        message: 'Book start reading',
        data: {
          reading_id: newReading._id
        }
      };
    });

    // ===== 事务成功，清除缓存并返回业务结果 =====
    ReadingCacheManager.clear(cacheKey, 'reading start success');
    res.json(businessResult);

  } catch (error) {
    console.error('Reading start transaction error:', error);
    // 错误情况清除缓存
    ReadingCacheManager.clear(cacheKey, 'transaction error');

    if (error.message && error.message.includes('库存')) {
      res.json({
        code: 401,
        message: 'Book not has stock',
        status: false
      });
    } else {
      res.json({
        code: 500,
        message: 'Failed to start reading',
        status: false
      });
    }
  } finally {
    await session.endSession();
  }
});

app.post('/record', upload.none(), async (req, res) => {
  if (!req.body.reading_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields ',
      status: false
    });
  }
  await Reading.findOne({
    _id: req.body.reading_id,
    returned: false
  }).then(async (resx) => {
    if (!resx) {
      res.json({
        code: 422,
        message: 'No data found',
        status: false
      });
      return;
    }

    await ReadingRecord.findOneAndUpdate({
      reading_id: mongoose.Types.ObjectId(req.body.reading_id)
    }, {
      read_time: new Date()
    }, {
      upsert: true
    });
    res.json({
      code: 200,
      message: 'Reading record created',
      status: true
    });
  });

  res.end();
});

app.post('/return', upload.none(), async (req, res) => {
  console.log('Reading return API called:', req.body.reading_id);

  // ===== 参数验证 =====
  if (!req.body.reading_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }

  // ===== 防重复提交机制 =====
  // Reading 归还基于 reading_id，防止同一阅读记录重复归还
  const cacheKey = 'reading_return_' + req.body.reading_id;

  // 检查是否正在处理中
  if (ReadingCacheManager.exists(cacheKey)) {
    return res.json({
      code: 418,
      message: 'Return processing in progress',
      status: false
    });
  }

  // 设置缓存标记，5分钟过期（Reading 操作相对简单，时间可以短一些）
  ReadingCacheManager.set(cacheKey, 'reading_return', 5);

  // 创建数据库会话用于事务
  const session = await mongoose.startSession();

  try {
    // ===== 使用事务确保数据一致性 =====
    await session.withTransaction(async () => {
      // ===== 第一步：验证阅读记录 =====
      const reading = await Reading.findOne({
        _id: req.body.reading_id
      }).populate({
        path: 'book_id',
        select: '_id'
      }).session(session);

      if (!reading) {
        ReadingCacheManager.clear(cacheKey, 'reading not found');
        return res.json({
          code: 422,
          message: 'Reading record not found',
          status: false
        });
      }

      if (reading.returned) {
        console.log(`Reading ${req.body.reading_id} already returned`);
        ReadingCacheManager.clear(cacheKey, 'already returned');
        return res.json({
          code: 200,
          message: 'Book already returned',
          status: true
        });
      }

      const book_id = reading.book_id._id;
      const client_id = reading.client_id;

      console.log(`Returning book - Client: ${client_id}, Book: ${book_id}, Reading ID: ${req.body.reading_id}, IP: ${reading.ip}`);

      // ===== 第二步：检查同一客户端的其他阅读记录 =====
      // Reading 模式下，同一 client_id 可能有多个相同书籍的阅读记录
      const otherReadings = await Reading.find({
        client_id: client_id,
        book_id: book_id,
        returned: false,
        _id: { $ne: req.body.reading_id }
      }).session(session);

      console.log(`Client ${client_id} has ${otherReadings.length} other active readings for book ${book_id}`);

      // ===== 第三步：更新阅读记录为已归还 =====
      await Reading.updateOne(
        { _id: req.body.reading_id },
        {
          $set: {
            returned: true,
            return_date: new Date()
          }
        },
        { session }
      );

      // ===== 第四步：增加书籍库存 =====
      // 事務內部：只使用原子操作，不再調用 isBookInStock()
      await BookHelper.updateAvailableStock(book_id, 'return', { session });

      // ===== 第五步：处理等待中的预约 =====
      // 关键业务逻辑：书籍归还后库存增加，需要检查是否有其他用户在等待预约
      // 如果有预约等待，自动将预约转换为借阅记录
      // 影响表：Reserve（标记预约为已处理）、Borrow（创建新的借阅记录）、Book（再次减少库存）
      await syncReturnBooks(book_id, { session });
    });

    // ===== 事务成功，清除缓存并返回结果 =====
    ReadingCacheManager.clear(cacheKey, 'return success');
    console.log('Reading book returned successfully:', req.body.reading_id);

    res.json({
      code: 200,
      message: 'Book returned successfully',
      status: true
    });

  } catch (error) {
    console.error('Reading return transaction error:', error);
    // 错误情况清除缓存
    ReadingCacheManager.clear(cacheKey, 'transaction error');

    res.json({
      code: 500,
      message: 'Failed to return book',
      status: false
    });
  } finally {
    await session.endSession();
  }
});

app.post('/countIp', upload.none(), async (req, res) => {
  const ip = req.header['cf-connecting-ip'] || req.ip;
  const countryCode = req.header('CF-IPCountry') || 'Unknown';
  const newReadingCount = new ReadingCount({
    ip: ip,
    client_id: req.body.client_id || null, // 兼容没有 client_id 的情况
    country_code: countryCode,
    create_at: new Date()
  });
  await newReadingCount.save();

  res.json({
    code: 200,
    message: 'ok',
    status: true
  });
  return res.end();
});

app.get('/countIp/stat', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const timeFilter = {};
    if (startDate || endDate) {
      timeFilter.create_at = {};
      if (startDate) {
        const ts = parseInt(startDate, 10);
        timeFilter.create_at.$gte = new Date(ts < 1e12 ? ts * 1000 : ts);
      }

      if (endDate) {
        const ts = parseInt(endDate, 10);
        timeFilter.create_at.$lte = new Date(ts < 1e12 ? ts * 1000 : ts);
      }
    }
    const pipeline = [
      {
        $facet: {
          hk: [
            { $match: { country_code: 'HK', ...timeFilter } },
            {
              $group: {
                _id: null,
                // 优先使用 client_id，如果没有则使用 ip
                uniqueIdentifiers: {
                  $addToSet: {
                    $cond: [
                      { $and: [{ $ne: ['$client_id', null] }, { $ne: ['$client_id', ''] }] },
                      { type: 'client', id: '$client_id' },
                      { type: 'ip', id: '$ip' }
                    ]
                  }
                },
                total: { $sum: 1 }
              }
            },
            {
              $project: {
                _id: 0,
                people: { $size: '$uniqueIdentifiers' },
                times: '$total'
              }
            }
          ],
          nonHk: [
            { $match: { country_code: { $ne: 'HK' }, ...timeFilter } },
            {
              $group: {
                _id: null,
                // 优先使用 client_id，如果没有则使用 ip
                uniqueIdentifiers: {
                  $addToSet: {
                    $cond: [
                      { $and: [{ $ne: ['$client_id', null] }, { $ne: ['$client_id', ''] }] },
                      { type: 'client', id: '$client_id' },
                      { type: 'ip', id: '$ip' }
                    ]
                  }
                },
                total: { $sum: 1 }
              }
            },
            {
              $project: {
                _id: 0,
                people: { $size: '$uniqueIdentifiers' },
                times: '$total'
              }
            }
          ]
        }
      }
    ];

    const result = await ReadingCount.aggregate(pipeline);
    const data = result[0];

    res.json({
      code: 200,
      message: 'ok',
      status: true,
      data: {
        hk: data.hk[0] || { people: 0, times: 0 },
        nonHk: data.nonHk[0] || { people: 0, times: 0 }
      }
    });
    return res.end();
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      code: 500,
      message: 'Internal server error',
      status: false
    });
  }
});


module.exports = {
  router: app,
  batchReturnReadingBooks
};
