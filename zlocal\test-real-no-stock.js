const mongoose = require('mongoose');
const Book = require('../Model/Book');
const Borrow = require('../Model/Borrow');
const Reserve = require('../Model/Reserve');
const User = require('../Model/User');
const BookHelper = require('../Helper/BookHelper');
const { syncReturnBooks } = require('../src/action/bookActions');

// 加载环境变量
require('dotenv').config();

// 连接数据库
mongoose.connect(process.env.DB_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function testRealNoStock() {
  console.log('=== 测试真实无库存场景（前置检查优化）===');

  try {
    // 查找一本有库存的书
    const book = await Book.findOne({
      available_quantity: { $gte: 1 },
      stock_quantity: { $gte: 1 }
    });

    if (!book) {
      console.log('没有找到有库存的书籍');
      return;
    }

    // 设置测试环境：1本总库存
    await Book.findByIdAndUpdate(book._id, {
      stock_quantity: 1,
      available_quantity: 1
    });

    console.log(`测试书籍: ${book.title}`);
    console.log('设置库存: 1/1');

    // 获取3个测试用户
    const testUsers = await User.find({ email: { $ne: '' } }).limit(3);
    if (testUsers.length < 3) {
      console.log('没有找到足够的测试用户');
      return;
    }

    console.log(`找到 ${testUsers.length} 个测试用户`);

    // 清理之前的测试数据
    await Reserve.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });
    await Borrow.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });

    // 创建一个借阅记录，使库存真正为0
    const borrowUser = testUsers[0];
    const existingBorrow = new Borrow({
      email: borrowUser.email,
      user_id: borrowUser._id,
      book_id: book._id,
      issue_date: new Date(),
      return_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
      returned: false,
      is_deleted: false
    });
    await existingBorrow.save();
    console.log(`用户1 (${borrowUser.email}) 已借阅此书`);

    // 创建2个预约（用户2和用户3）
    const reservations = [];
    for (let i = 1; i < 3; i++) {
      const user = testUsers[i];
      const reserve = new Reserve({
        user_id: user._id,
        book_id: book._id,
        email: user.email,
        reserve_date: new Date(),
        status: true,
        is_mailed: false,
        is_deleted: false,
        is_blocked: false
      });
      await reserve.save();
      reservations.push(reserve);
      console.log(`用户${i + 1} (${user.email}) 创建预约`);
    }

    console.log('\n现在情况：1本书被借出，2个用户预约');
    console.log('期望结果：两个预约用户都无法借阅（前置检查直接拒绝）');

    // 测试前置检查优化
    console.log('\n=== 测试前置检查优化 ===');

    // 检查当前实际可用库存
    const currentStock = await BookHelper.isBookInStock(book._id.toString());
    console.log(`当前实际可用库存: ${currentStock}`);

    // 直接测试 updateAvailableStock 函数
    console.log('\n1. 直接测试 updateAvailableStock 函数:');
    try {
      await BookHelper.updateAvailableStock(book._id.toString(), 'borrow');
      console.log('❌ 应该抛出错误但没有抛出');
    } catch (error) {
      console.log(`✅ 正确抛出错误: ${error.message}`);
    }

    // 测试 syncReturnBooks 函数
    console.log('\n2. 测试 syncReturnBooks 函数:');
    try {
      await syncReturnBooks(book._id.toString());
    } catch (error) {
      console.log('执行完成，可能有错误:', error.message);
    }

    // 检查结果
    const finalBook = await Book.findById(book._id);
    const borrowRecords = await Borrow.find({
      book_id: book._id,
      returned: false,
      user_id: { $in: testUsers.map(u => u._id) }
    });

    const processedReserves = await Reserve.find({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) },
      is_deleted: true
    });

    const remainingReserves = await Reserve.find({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) },
      is_deleted: false
    });

    console.log('\n=== 测试结果 ===');
    console.log(`最终库存: ${finalBook.available_quantity}/${finalBook.stock_quantity}`);
    console.log(`总借阅记录数: ${borrowRecords.length}`);
    console.log(`已处理预约数: ${processedReserves.length}`);
    console.log(`剩余预约数: ${remainingReserves.length}`);

    // 验证前置检查的效果
    console.log('\n=== 验证前置检查优化效果 ===');

    if (borrowRecords.length === 1) {
      console.log('✅ 只有1个借阅记录（原有的借阅）');
    } else {
      console.log(`❌ 借阅记录数异常: ${borrowRecords.length}`);
    }

    if (remainingReserves.length === 2) {
      console.log('✅ 所有预约保持未处理状态（前置检查生效）');
    } else {
      console.log(`❌ 预约状态异常: ${remainingReserves.length}`);
    }

    // 测试归还后的情况
    console.log('\n=== 测试归还后的情况 ===');

    // 归还书籍
    await Borrow.findByIdAndUpdate(existingBorrow._id, { returned: true });
    console.log('用户1归还了书籍');

    // 再次检查库存
    const stockAfterReturn = await BookHelper.isBookInStock(book._id.toString());
    console.log(`归还后实际可用库存: ${stockAfterReturn}`);

    // 测试现在是否可以借阅
    try {
      await BookHelper.updateAvailableStock(book._id.toString(), 'borrow');
      console.log('✅ 归还后成功减少库存');
    } catch (error) {
      console.log(`❌ 归还后仍然失败: ${error.message}`);
    }

    // 清理测试数据
    await Borrow.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });
    await Reserve.deleteMany({
      book_id: book._id,
      user_id: { $in: testUsers.map(u => u._id) }
    });

    // 恢复原始库存
    await Book.findByIdAndUpdate(book._id, {
      available_quantity: book.stock_quantity
    });

    console.log('\n测试数据已清理');

  } catch (error) {
    console.error('测试过程中出错:', error);
  } finally {
    await mongoose.connection.close();
  }
}

// 运行测试
testRealNoStock();
