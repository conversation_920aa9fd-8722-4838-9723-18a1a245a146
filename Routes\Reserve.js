const app = require('express').Router();
const { UserHelper } = require('../Helper/UserHelper');
const Book = require('../Model/Book');
const User = require('../Model/User');
const Reserve = require('../Model/Reserve');
const BookHelper = require('../Helper/BookHelper');
const { Types } = require('mongoose');
const { MAX_RESERVE_BOOK_NUM } = require('../consts');

app.use('/*', (req, res, next) => next());

app.post('/reserveBook', UserHelper, async (req, res) => {
  if (!req.body.user_id || !req.body.book_id || !req.body.issue_date || !req.body.email) {
    return res.json({
      code: 418,
      message: 'Missing required fields',
      status: false
    });
  }
  const book = await Book.findOne({
    _id: new Types.ObjectId(req.body.book_id)
  });
  // 修复版本 - 移除不必要的转义
  const expression = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  const valid = expression.test(String(req.body.email).toLowerCase());
  if (valid == true) {
    await User.find({ email: req.body.email, _id: { $ne: req.body.user_id } }).then(async (resx) => {
      await User.findByIdAndUpdate(
        req.body.user_id,
        { email: req.body.email },
        async (err, updated) => {
          if (err) {
            console.log('Internal error in update Email');
            res.json({
              code: 500,
              message: 'Internal error in update Email',
              success: false
            });
            return res.end();
          }
          else {
            if (await BookHelper.totalBookReservedInPresent(req.body.user_id, book.collection_type) >= MAX_RESERVE_BOOK_NUM) {
              res.json({
                code: 428,
                message: 'You\'ve reached maximum limit of reserve!',
                status: false
              });
              return res.end();
            }
            Reserve.find({ user_id: req.body.user_id, book_id: req.body.book_id, is_deleted: false }).then(async (resx) => {
              if (resx != '') {
                return res.json({
                  code: 422,
                  message: 'You\'ve already reserve this!',
                  status: false
                });
              }
              else {
                const user_id = req.body.user_id;
                const book_id = req.body.book_id;
                const email = req.body.email;
                const rReserve = Reserve({
                  user_id: req.body.user_id,
                  email: req.body.email,
                  book_id: req.body.book_id,
                  reserve_date: (req.body.issue_date).toString(),
                  status: true,
                  is_deleted: false
                });
                rReserve.save().then((stored) => {
                  User.findOne({ _id: user_id }).then((userData) => {
                    if (userData) {
                      Book.findOne({ _id: book_id }).then((bookData) => {
                        if (email) {
                          // mailOptions = {
                          //   to: email,
                          //   from: '<EMAIL>',
                          //   subject: 'Book Reserve : Enrich Culture Group',
                          //   text: 'Hi, You Have Reserve this Book "' + bookData.title + '", which is not available currently, we will send you a notification when this book will be available.'
                          // };
                          //mailer(mailOptions);
                        }
                      });
                    }
                  });
                  return res.json({
                    code: 200,
                    data: stored,
                    message: 'Operation successful.'
                  });
                }).catch((err) => {
                  return res.json({
                    code: 500,
                    message: 'Internal Error in borrowing.',
                    status: false
                  });
                });
              }
            });
          }
        }
      );
    });
  } else {
    return res.json({
      code: 424,
      message: 'Enter valid Email-id',
      status: false
    });
  }
});

app.post('/detail', UserHelper, async (req, res) => {

  if (!req.body.user_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }

  const resp = await Reserve.find({ user_id: req.body.user_id, is_deleted: false, is_mailed: false }).sort({ _id: -1 }).populate('book_id');
  if (!resp) {
    return res.json({
      code: 422,
      message: 'No data found',
      status: false
    });
  }
  const response = [];
  for (const borrowData of resp) {
    if (borrowData.book_id) {
      const data = { ...borrowData._doc };
      response.push(data);
    }
  }
  res.json({
    code: 200,
    message: 'Operation successful.',
    data: response
  });
  return res.end();
});

app.post('/deleteReserveBook', UserHelper, async (req, res) => {
  if (!req.body.delete_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }

  const doc = await Reserve.findOneAndUpdate(
    { _id: req.body.delete_id }, { is_deleted: true }
  );

  return res.json({
    code: 200,
    data: false,
    message: 'Operation successful.'
  });

});
app.post('/allReservedeleteBook', UserHelper, async (req, res) => {
  if (!req.body.delete_id) {
    return res.json({
      code: 422,
      message: 'Missing required fields',
      status: false
    });
  }
  const bookbulk = [];


  const promises1 = req.body.delete_id.map(async (obj) => {
    bookbulk.push(obj);
  });
  const results1 = await Promise.all(promises1);
  const options = { ordered: true };
  const query = { _id: bookbulk };
  const data = { $set: { is_deleted: true } };
  const result = await Reserve.updateMany(query, data);
  return res.json({
    code: 200,
    data: req.body.delete_id,
    message: 'Operation successful.'
  });

});
module.exports = {
  router: app
};
