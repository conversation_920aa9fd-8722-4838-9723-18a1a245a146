/**
 * 併發借書測試腳本
 * 測試場景：書籍總共3本，4個用戶同時嘗試借書
 * 預期結果：前3個用戶成功，第4個用戶失敗
 */

const mongoose = require('mongoose');
const Book = require('../Model/Book');
const User = require('../Model/User');
const Borrow = require('../Model/Borrow');
const BookHelper = require('../Helper/BookHelper');

// 模擬用戶數據
const testUsers = [
  { _id: new mongoose.Types.ObjectId(), email: '<EMAIL>', name: 'User 1' },
  { _id: new mongoose.Types.ObjectId(), email: '<EMAIL>', name: 'User 2' },
  { _id: new mongoose.Types.ObjectId(), email: '<EMAIL>', name: 'User 3' },
  { _id: new mongoose.Types.ObjectId(), email: '<EMAIL>', name: 'User 4' }
];

// 測試書籍ID（需要替換為實際的書籍ID）
let testBookId = null;

/**
 * 模擬併發借書請求
 */
async function simulateConcurrentBorrow(userId, bookId, userIndex) {
  const session = await mongoose.startSession();
  
  try {
    console.log(`用戶${userIndex + 1} 開始嘗試借書...`);
    
    const result = await session.withTransaction(async () => {
      // 檢查庫存
      const stockBefore = await BookHelper.isBookInStock(bookId);
      console.log(`用戶${userIndex + 1} 檢查庫存: ${stockBefore}`);
      
      if (stockBefore <= 0) {
        throw new Error('STOCK_OUT');
      }
      
      // 檢查用戶是否已經借過這本書
      const existingBorrow = await Borrow.findOne({
        user_id: userId,
        book_id: bookId,
        returned: false,
        is_deleted: false
      }).session(session);
      
      if (existingBorrow) {
        throw new Error('ALREADY_BORROWED');
      }
      
      // 原子操作：檢查庫存並減少
      const bookUpdateResult = await Book.findOneAndUpdate(
        {
          _id: mongoose.Types.ObjectId(bookId),
          available_quantity: { $gt: 0 }
        },
        { $inc: { available_quantity: -1 } },
        { 
          new: true,
          session: session
        }
      );

      if (!bookUpdateResult) {
        throw new Error('STOCK_OUT');
      }

      // 創建借閱記錄
      const issueDate = new Date();
      const returnDate = new Date();
      returnDate.setDate(returnDate.getDate() + 7); // 7天後歸還

      const mBorrow = new Borrow({
        email: testUsers[userIndex].email,
        user_id: userId,
        book_id: bookId,
        issue_date: issueDate,
        return_date: returnDate,
        returned: false,
        is_deleted: false
      });

      await mBorrow.save({ session });
      
      console.log(`用戶${userIndex + 1} 借書成功！庫存更新為: ${bookUpdateResult.available_quantity}`);
      return { success: true, borrowId: mBorrow._id };
    });
    
    return result;
    
  } catch (error) {
    console.log(`用戶${userIndex + 1} 借書失敗: ${error.message}`);
    return { success: false, error: error.message };
  } finally {
    await session.endSession();
  }
}

/**
 * 模擬歸還書籍
 */
async function simulateReturnBook(borrowId, userIndex) {
  const session = await mongoose.startSession();
  
  try {
    console.log(`用戶${userIndex + 1} 開始歸還書籍...`);
    
    await session.withTransaction(async () => {
      // 查找借閱記錄
      const borrowRecord = await Borrow.findOne({
        _id: borrowId,
        returned: false
      }).session(session);

      if (!borrowRecord) {
        throw new Error('BORROW_NOT_FOUND');
      }

      const book_id = borrowRecord.book_id;

      // 更新借閱記錄為已歸還
      await Borrow.findOneAndUpdate({
        _id: borrowId
      }, {
        returned: true,
        return_date: new Date()
      }, {
        useFindAndModify: false,
        session: session
      });

      // 更新庫存（增加可用數量）
      await BookHelper.updateAvailableStock(book_id, 'return', { session });
      
      const stockAfter = await BookHelper.isBookInStock(book_id);
      console.log(`用戶${userIndex + 1} 歸還成功！當前庫存: ${stockAfter}`);
    });
    
    return { success: true };
    
  } catch (error) {
    console.log(`用戶${userIndex + 1} 歸還失敗: ${error.message}`);
    return { success: false, error: error.message };
  } finally {
    await session.endSession();
  }
}

/**
 * 主測試函數
 */
async function runConcurrentBorrowTest() {
  try {
    console.log('=== 開始併發借書測試 ===\n');
    
    // 1. 連接數據庫
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/jrc-service');
    console.log('數據庫連接成功');
    
    // 2. 查找一本測試書籍
    const testBook = await Book.findOne({ 
      is_deleted: { $ne: true },
      stock_quantity: { $gte: 3 }
    });
    
    if (!testBook) {
      console.log('未找到合適的測試書籍（需要庫存 >= 3）');
      return;
    }
    
    testBookId = testBook._id;
    console.log(`使用測試書籍: ${testBook.title} (ID: ${testBookId})`);
    console.log(`初始庫存: ${testBook.stock_quantity}, 可用: ${testBook.available_quantity}\n`);
    
    // 3. 確保書籍有3本可用庫存
    await Book.findOneAndUpdate(
      { _id: testBookId },
      { 
        stock_quantity: 3,
        available_quantity: 3
      }
    );
    
    // 4. 清理之前的測試數據
    await Borrow.deleteMany({
      book_id: testBookId,
      user_id: { $in: testUsers.map(u => u._id) }
    });
    
    console.log('測試數據準備完成\n');
    
    // 5. 模擬4個用戶同時借書
    console.log('=== 開始併發借書測試 ===');
    const borrowPromises = testUsers.map((user, index) => 
      simulateConcurrentBorrow(user._id, testBookId, index)
    );
    
    const borrowResults = await Promise.all(borrowPromises);
    
    // 6. 統計結果
    const successCount = borrowResults.filter(r => r.success).length;
    const failCount = borrowResults.filter(r => !r.success).length;
    
    console.log(`\n=== 借書結果統計 ===`);
    console.log(`成功: ${successCount} 人`);
    console.log(`失敗: ${failCount} 人`);
    
    // 7. 檢查最終庫存
    const finalBook = await Book.findOne({ _id: testBookId });
    console.log(`最終庫存: ${finalBook.available_quantity}/${finalBook.stock_quantity}\n`);
    
    // 8. 模擬用戶1歸還書籍
    const successfulBorrows = borrowResults.filter(r => r.success);
    if (successfulBorrows.length > 0) {
      console.log('=== 模擬用戶1歸還書籍 ===');
      await simulateReturnBook(successfulBorrows[0].borrowId, 0);
      
      // 9. 模擬用戶4再次嘗試借書
      console.log('\n=== 用戶4再次嘗試借書 ===');
      const retryResult = await simulateConcurrentBorrow(testUsers[3]._id, testBookId, 3);
      
      if (retryResult.success) {
        console.log('用戶4 重試借書成功！');
      } else {
        console.log(`用戶4 重試借書失敗: ${retryResult.error}`);
      }
    }
    
    console.log('\n=== 測試完成 ===');
    
  } catch (error) {
    console.error('測試過程中發生錯誤:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// 如果直接運行此腳本
if (require.main === module) {
  runConcurrentBorrowTest();
}

module.exports = {
  runConcurrentBorrowTest,
  simulateConcurrentBorrow,
  simulateReturnBook
};
